import "next";
import { loadEnvConfig } from "@next/env";
import * as matchers from "@testing-library/jest-dom/matchers";
import { cleanup } from "@testing-library/react";
import { afterEach, expect, vi } from "vitest";
import "@testing-library/jest-dom/vitest";

// Set up test environment variables before loading config
if (!process.env.NODE_ENV) {
	Object.defineProperty(process.env, "NODE_ENV", { value: "test", writable: true });
}
process.env.SECRET = "12345678901234567890123456789012"; // Exactly 32 characters for JWT
process.env.EMAIL = "<EMAIL>";
process.env.PASSWORD = "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBcQGQ9xPWvHDe"; // bcrypt hash of "testPassword123"
process.env.STRIPE_CURRENCY = "usd";

// Load Next.js environment configuration
loadEnvConfig(".");

// Mock next/headers for all tests
vi.mock("next/headers", () => ({
	cookies: vi.fn(),
}));

// Mock next/navigation for all tests
vi.mock("next/navigation", () => ({
	redirect: vi.fn(),
}));

/**
 * Vitest setup logic
 * https://vitest.dev/config/#setupfiles
 */

expect.extend(matchers);

afterEach(() => {
	cleanup();
});
