import { revalidateTag } from "next/cache";

export function invalidateSearchCache(): void {
	try {
		revalidateTag("products");
		revalidateTag("search");
		console.log("Search cache invalidated successfully");
	} catch (error) {
		console.error("Failed to invalidate search cache:", error);
	}
}

export function invalidateProductsCache(): void {
	try {
		revalidateTag("products");
		console.log("Products cache invalidated successfully");
	} catch (error) {
		console.error("Failed to invalidate products cache:", error);
	}
}

export function invalidateAllCaches(): void {
	try {
		revalidateTag("products");
		revalidateTag("search");
		revalidateTag("cart");
		console.log("All caches invalidated successfully");
	} catch (error) {
		console.error("Failed to invalidate all caches:", error);
	}
}

export interface CacheInvalidationOptions {
	products?: boolean;
	search?: boolean;
	cart?: boolean;
}

export function invalidateCache(options: CacheInvalidationOptions): void {
	try {
		const tags: string[] = [];

		if (options.products) tags.push("products");
		if (options.search) tags.push("search");
		if (options.cart) tags.push("cart");

		tags.forEach((tag) => revalidateTag(tag));

		console.log(`Cache invalidated for tags: ${tags.join(", ")}`);
	} catch (error) {
		console.error("Failed to invalidate cache:", error);
	}
}
