import { describe, expect, it } from "vitest";
import { hashPassword, verifyPassword } from "./password";
import { isValidPasswordHash, validatePasswordRequirements } from "./password-utils";

describe("Password Utilities", () => {
	describe("hashPassword", () => {
		it("should hash a password", async () => {
			const password = "testPassword123!";
			const hashedPassword = await hashPassword(password);

			expect(hashedPassword).not.toBe(password);
			expect(hashedPassword).toMatch(/^\$2[aby]\$/);
			expect(hashedPassword.length).toBeGreaterThan(50);
		});

		it("should generate different hashes for the same password", async () => {
			const password = "testPassword123!";
			const hash1 = await hashPassword(password);
			const hash2 = await hashPassword(password);

			expect(hash1).not.toBe(hash2);
		});
	});

	describe("verifyPassword", () => {
		it("should verify correct password", async () => {
			const password = "testPassword123!";
			const hashedPassword = await hashPassword(password);

			const isValid = await verifyPassword(password, hashedPassword);

			expect(isValid).toBe(true);
		});

		it("should reject incorrect password", async () => {
			const password = "testPassword123!";
			const wrongPassword = "wrongPassword123!";
			const hashedPassword = await hashPassword(password);

			const isValid = await verifyPassword(wrongPassword, hashedPassword);

			expect(isValid).toBe(false);
		});

		it("should reject empty password", async () => {
			const password = "testPassword123!";
			const hashedPassword = await hashPassword(password);

			const isValid = await verifyPassword("", hashedPassword);

			expect(isValid).toBe(false);
		});
	});

	describe("validatePasswordRequirements", () => {
		it("should accept valid password with default requirements", () => {
			const result = validatePasswordRequirements("Password123");

			expect(result.valid).toBe(true);
			expect(result.errors).toHaveLength(0);
		});

		it("should reject password too short", () => {
			const result = validatePasswordRequirements("Pass1");

			expect(result.valid).toBe(false);
			expect(result.errors).toContain("Password must be at least 8 characters long");
		});

		it("should reject password without uppercase", () => {
			const result = validatePasswordRequirements("password123");

			expect(result.valid).toBe(false);
			expect(result.errors).toContain("Password must contain at least one uppercase letter");
		});

		it("should reject password without lowercase", () => {
			const result = validatePasswordRequirements("PASSWORD123");

			expect(result.valid).toBe(false);
			expect(result.errors).toContain("Password must contain at least one lowercase letter");
		});

		it("should reject password without numbers", () => {
			const result = validatePasswordRequirements("Password");

			expect(result.valid).toBe(false);
			expect(result.errors).toContain("Password must contain at least one number");
		});

		it("should accept password without special characters when not required", () => {
			const result = validatePasswordRequirements("Password123");

			expect(result.valid).toBe(true);
			expect(result.errors).toHaveLength(0);
		});

		it("should reject password without special characters when required", () => {
			const result = validatePasswordRequirements("Password123", {
				minLength: 8,
				requireUppercase: true,
				requireLowercase: true,
				requireNumbers: true,
				requireSpecialChars: true,
			});

			expect(result.valid).toBe(false);
			expect(result.errors).toContain("Password must contain at least one special character");
		});

		it("should accept password with special characters when required", () => {
			const result = validatePasswordRequirements("Password123!", {
				minLength: 8,
				requireUppercase: true,
				requireLowercase: true,
				requireNumbers: true,
				requireSpecialChars: true,
			});

			expect(result.valid).toBe(true);
			expect(result.errors).toHaveLength(0);
		});

		it("should work with custom requirements", () => {
			const result = validatePasswordRequirements("pass", {
				minLength: 4,
				requireUppercase: false,
				requireLowercase: true,
				requireNumbers: false,
				requireSpecialChars: false,
			});

			expect(result.valid).toBe(true);
			expect(result.errors).toHaveLength(0);
		});

		it("should accumulate multiple errors", () => {
			const result = validatePasswordRequirements("p");

			expect(result.valid).toBe(false);
			expect(result.errors.length).toBeGreaterThan(1);
			expect(result.errors).toContain("Password must be at least 8 characters long");
			expect(result.errors).toContain("Password must contain at least one uppercase letter");
			expect(result.errors).toContain("Password must contain at least one number");
		});
	});

	describe("isValidPasswordHash", () => {
		it("should recognize valid bcrypt hash", () => {
			const validHash = "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LeWZdRlQU0rTh4q2i";

			expect(isValidPasswordHash(validHash)).toBe(true);
		});

		it("should recognize valid bcrypt hash with different variants", () => {
			const validHashA = "$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LeWZdRlQU0rTh4q2i";
			const validHashY = "$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LeWZdRlQU0rTh4q2i";

			expect(isValidPasswordHash(validHashA)).toBe(true);
			expect(isValidPasswordHash(validHashY)).toBe(true);
		});

		it("should reject plain text password", () => {
			expect(isValidPasswordHash("plainTextPassword")).toBe(false);
		});

		it("should reject invalid hash format", () => {
			expect(isValidPasswordHash("$1$invalid$hash")).toBe(false);
		});

		it("should reject empty string", () => {
			expect(isValidPasswordHash("")).toBe(false);
		});

		it("should reject hash that's too short", () => {
			expect(isValidPasswordHash("$2b$12$short")).toBe(false);
		});
	});
});
