import * as Commerce from "commerce-kit";
import { notFound } from "next/navigation";
import StoreConfig, { type Category } from "@/store.config";

export function getCategoryBySlug(slug: string): Category | undefined {
	return StoreConfig.categories.find((category) => category.slug === slug);
}

export async function getCategoryWithProducts(slug: string): Promise<{
	category: Category;
	products: Awaited<ReturnType<typeof Commerce.productBrowse>>;
}> {
	const category = getCategoryBySlug(slug);
	if (!category) {
		notFound();
	}

	let products: Awaited<ReturnType<typeof Commerce.productBrowse>> = [];

	try {
		products = await Commerce.productBrowse({
			first: 100,
			filter: { category: slug },
		});
	} catch (error) {
		console.error(`Failed to fetch products for category "${slug}":`, error);
		notFound();
	}

	if (products.length === 0) {
		notFound();
	}

	return { category, products };
}

export function getVisibleCategories(): Category[] {
	return StoreConfig.categories.filter((category) => category.isVisible).sort((a, b) => a.order - b.order);
}

export function getAllCategories(): readonly Category[] {
	return StoreConfig.categories;
}

export function getCategorySlugs(): string[] {
	return StoreConfig.categories.map((category) => category.slug);
}

export function getVisibleCategorySlugs(): string[] {
	return getVisibleCategories().map((category) => category.slug);
}

export function isCategoryVisible(slug: string): boolean {
	const category = getCategoryBySlug(slug);
	return category?.isVisible ?? false;
}

export function getCategoriesOrderedByName(): Category[] {
	return [...StoreConfig.categories].sort((a, b) => a.name.localeCompare(b.name));
}
