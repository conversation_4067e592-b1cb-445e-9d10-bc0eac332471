import { describe, expect, it } from "vitest";
import {
	getAllCategories,
	getCategoriesOrderedByName,
	getCategoryBySlug,
	getCategorySlugs,
	getVisibleCategories,
	getVisibleCategorySlugs,
	isCategoryVisible,
} from "./categories";

describe("categories utilities", () => {
	describe("getCategoryBySlug", () => {
		it("should return category for valid slug", () => {
			const category = getCategoryBySlug("apparel");
			expect(category).toBeDefined();
			expect(category?.name).toBe("Apparel");
			expect(category?.slug).toBe("apparel");
		});

		it("should return undefined for invalid slug", () => {
			const category = getCategoryBySlug("nonexistent");
			expect(category).toBeUndefined();
		});

		it("should return undefined for empty slug", () => {
			const category = getCategoryBySlug("");
			expect(category).toBeUndefined();
		});

		it("should be case sensitive", () => {
			const category = getCategoryBySlug("APPAREL");
			expect(category).toBeUndefined();
		});
	});

	describe("getVisibleCategories", () => {
		it("should return only visible categories", () => {
			const categories = getVisibleCategories();
			expect(categories.length).toBeGreaterThanOrEqual(2);
			expect(categories.every((cat) => cat.isVisible)).toBe(true);
		});

		it("should return categories sorted by order", () => {
			const categories = getVisibleCategories();
			// Should be sorted by order field (apparel=1, accessories=2)
			expect(categories[0].slug).toBe("apparel");
			expect(categories[1].slug).toBe("accessories");
		});

		it("should return array type", () => {
			const categories = getVisibleCategories();
			expect(Array.isArray(categories)).toBe(true);
		});
	});

	describe("getAllCategories", () => {
		it("should return all categories from config", () => {
			const categories = getAllCategories();
			expect(categories.length).toBe(2); // Current config has 2 categories
			expect(categories.find((cat) => cat.slug === "apparel")).toBeDefined();
			expect(categories.find((cat) => cat.slug === "accessories")).toBeDefined();
		});

		it("should return readonly array", () => {
			const categories = getAllCategories();
			expect(categories).toBeDefined();
		});
	});

	describe("getCategorySlugs", () => {
		it("should return array of all category slugs", () => {
			const slugs = getCategorySlugs();
			expect(slugs).toContain("apparel");
			expect(slugs).toContain("accessories");
			expect(slugs.length).toBe(2);
		});

		it("should return string array", () => {
			const slugs = getCategorySlugs();
			expect(Array.isArray(slugs)).toBe(true);
			expect(slugs.every((slug) => typeof slug === "string")).toBe(true);
		});
	});

	describe("getVisibleCategorySlugs", () => {
		it("should return only visible category slugs in order", () => {
			const slugs = getVisibleCategorySlugs();
			expect(slugs).toEqual(["apparel", "accessories"]); // sorted by order
		});

		it("should maintain order from getVisibleCategories", () => {
			const slugs = getVisibleCategorySlugs();
			const categories = getVisibleCategories();
			expect(slugs).toEqual(categories.map((cat) => cat.slug));
		});
	});

	describe("isCategoryVisible", () => {
		it("should return true for visible categories", () => {
			expect(isCategoryVisible("apparel")).toBe(true);
			expect(isCategoryVisible("accessories")).toBe(true);
		});

		it("should return false for nonexistent categories", () => {
			expect(isCategoryVisible("nonexistent")).toBe(false);
		});

		it("should return false for empty slug", () => {
			expect(isCategoryVisible("")).toBe(false);
		});
	});

	describe("getCategoriesOrderedByName", () => {
		it("should return categories sorted alphabetically by name", () => {
			const categories = getCategoriesOrderedByName();
			expect(categories[0].name).toBe("Accessories");
			expect(categories[1].name).toBe("Apparel");
		});

		it("should include all categories", () => {
			const categories = getCategoriesOrderedByName();
			expect(categories.length).toBe(2);
		});

		it("should not mutate original array", () => {
			const original = getAllCategories();
			const sorted = getCategoriesOrderedByName();
			expect(original[0].name).toBe("Apparel"); // Original order maintained
			expect(sorted[0].name).toBe("Accessories"); // Sorted order
		});
	});
});
