interface PerformanceMetric {
	endpoint: string;
	method: string;
	duration: number;
	status: number;
	timestamp: number;
	userAgent?: string;
	ip?: string;
}

interface ErrorMetric {
	endpoint: string;
	method: string;
	error: string;
	timestamp: number;
	userAgent?: string;
	ip?: string;
}

/**
 * Simple in-memory storage for metrics
 *
 * ⚠️ IMPORTANT: This is suitable for development and small-scale deployments only!
 *
 * Limitations:
 * - Data is lost on server restarts
 * - Memory usage grows with traffic
 * - No persistence across deployments
 * - Limited to single server instance
 *
 * For production at scale, consider:
 * - Redis for distributed caching
 * - Database storage for persistence
 * - Dedicated metrics services (DataDog, New Relic, Prometheus)
 * - Cloud monitoring (Vercel Analytics, AWS CloudWatch)
 *
 * Current implementation limits to 1000 metrics per type to prevent memory issues.
 */
const performanceMetrics: PerformanceMetric[] = [];
const errorMetrics: ErrorMetric[] = [];
const MAX_STORED_METRICS = 1000;

// Performance thresholds
const SLOW_REQUEST_THRESHOLD = 1000; // 1 second
const VERY_SLOW_REQUEST_THRESHOLD = 3000; // 3 seconds

export function logPerformanceMetric(metric: PerformanceMetric): void {
	// Add to metrics array
	performanceMetrics.push(metric);

	// Keep only latest metrics to prevent memory issues
	if (performanceMetrics.length > MAX_STORED_METRICS) {
		performanceMetrics.splice(0, performanceMetrics.length - MAX_STORED_METRICS);
	}

	// Log slow requests for immediate attention
	if (metric.duration > VERY_SLOW_REQUEST_THRESHOLD) {
		console.warn(`🐌 Very slow request detected:`, {
			endpoint: metric.endpoint,
			method: metric.method,
			duration: `${metric.duration}ms`,
			status: metric.status,
		});
	} else if (metric.duration > SLOW_REQUEST_THRESHOLD) {
		console.warn(`⚠️ Slow request detected:`, {
			endpoint: metric.endpoint,
			method: metric.method,
			duration: `${metric.duration}ms`,
			status: metric.status,
		});
	}
}

export function logErrorMetric(metric: ErrorMetric): void {
	errorMetrics.push(metric);

	// Keep only latest error metrics
	if (errorMetrics.length > MAX_STORED_METRICS) {
		errorMetrics.splice(0, errorMetrics.length - MAX_STORED_METRICS);
	}

	console.error(`❌ API Error:`, {
		endpoint: metric.endpoint,
		method: metric.method,
		error: metric.error,
		timestamp: new Date(metric.timestamp).toISOString(),
	});
}

export function getPerformanceStats(): {
	totalRequests: number;
	averageResponseTime: number;
	slowRequests: number;
	errorRate: number;
	recentErrors: ErrorMetric[];
} {
	const totalRequests = performanceMetrics.length;
	const slowRequests = performanceMetrics.filter((m) => m.duration > SLOW_REQUEST_THRESHOLD).length;
	const totalErrors = errorMetrics.length;

	const averageResponseTime =
		totalRequests > 0 ? performanceMetrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests : 0;

	const errorRate = totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;

	const recentErrors = errorMetrics.slice(-10); // Last 10 errors

	return {
		totalRequests,
		averageResponseTime: Math.round(averageResponseTime),
		slowRequests,
		errorRate: Math.round(errorRate * 100) / 100,
		recentErrors,
	};
}

export function createPerformanceMiddleware() {
	return function performanceWrapper<T extends (...args: unknown[]) => Promise<Response>>(
		handler: T,
		endpoint: string,
	): T {
		return (async (...args: unknown[]) => {
			const startTime = Date.now();
			const request = args[0] as { method?: string; headers?: { get?: (key: string) => string | null } };
			const method = request?.method || "GET";

			try {
				const response = await handler(...args);
				const duration = Date.now() - startTime;

				logPerformanceMetric({
					endpoint,
					method,
					duration,
					status: response.status,
					timestamp: startTime,
					userAgent: request?.headers?.get?.("user-agent") || undefined,
					ip:
						request?.headers?.get?.("x-forwarded-for")?.split(",")[0]?.trim() ||
						request?.headers?.get?.("x-real-ip") ||
						undefined,
				});

				return response;
			} catch (error) {
				const duration = Date.now() - startTime;
				const errorMessage = error instanceof Error ? error.message : String(error);

				logErrorMetric({
					endpoint,
					method,
					error: errorMessage,
					timestamp: startTime,
					userAgent: request?.headers?.get?.("user-agent") || undefined,
					ip:
						request?.headers?.get?.("x-forwarded-for")?.split(",")[0]?.trim() ||
						request?.headers?.get?.("x-real-ip") ||
						undefined,
				});

				// Also log as performance metric with error status
				logPerformanceMetric({
					endpoint,
					method,
					duration,
					status: 500,
					timestamp: startTime,
				});

				throw error;
			}
		}) as T;
	};
}

// Simple timer utility for measuring operation performance
export class PerformanceTimer {
	private startTime: number;
	private label: string;

	constructor(label: string) {
		this.label = label;
		this.startTime = Date.now();
	}

	end(): number {
		const duration = Date.now() - this.startTime;

		if (duration > SLOW_REQUEST_THRESHOLD) {
			console.warn(`⏱️ Slow operation "${this.label}": ${duration}ms`);
		}

		return duration;
	}
}

// Utility to time async operations
export async function timeOperation<T>(
	label: string,
	operation: () => Promise<T>,
): Promise<{ result: T; duration: number }> {
	const timer = new PerformanceTimer(label);

	try {
		const result = await operation();
		const duration = timer.end();
		return { result, duration };
	} catch (error) {
		const duration = timer.end();
		console.error(`❌ Operation "${label}" failed after ${duration}ms:`, error);
		throw error;
	}
}

// Export for testing - clear metrics in test environment
export function clearMetrics(): void {
	if (process.env.NODE_ENV === "test") {
		performanceMetrics.length = 0;
		errorMetrics.length = 0;
	}
}
