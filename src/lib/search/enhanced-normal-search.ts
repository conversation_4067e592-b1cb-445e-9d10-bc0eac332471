import type * as Commerce from "commerce-kit";
import Fuse, { type IFuseOptions } from "fuse.js";
import { unstable_cache } from "next/cache";
import { safeProductBrowse } from "@/lib/safe-commerce";

// Configuration constants for search behavior
const FUSE_THRESHOLD = 0.4; // Lower = more strict matching (range: 0-1)
const METADATA_BATCH_SIZE = 100; // Number of products to fetch per batch
const MAX_METADATA_PRODUCTS = 500; // Maximum products to load for metadata extraction
const SMALL_CATALOG_THRESHOLD = 500; // Threshold for in-memory vs live search
const LIVE_SEARCH_BATCH_SIZE = 100; // Batch size for live search pagination
const LIVE_SEARCH_MAX_OFFSET = 2000; // Safety limit to prevent excessive API calls
const CACHE_TTL = 3600; // Cache time-to-live in seconds (1 hour)

export interface NormalSearchFilters {
	category?: string;
	priceRange?: {
		min: number;
		max: number;
	};
	sortBy?: "relevance" | "price-asc" | "price-desc" | "name" | "newest";
}

export interface NormalSearchResult {
	products: Commerce.MappedProduct[];
	totalCount: number;
	categories: string[];
	priceRange: {
		min: number;
		max: number;
	};
	error?: string;
}

export interface NormalSearchOptions {
	query: string;
	filters?: NormalSearchFilters;
	limit?: number;
	offset?: number;
}

// Fuse.js configuration for fuzzy search
const fuseOptions: IFuseOptions<Commerce.MappedProduct> = {
	keys: [
		{ name: "name", weight: 0.7 },
		{ name: "description", weight: 0.3 },
		{ name: "metadata.category", weight: 0.5 },
		{ name: "metadata.variant", weight: 0.2 },
	],
	threshold: FUSE_THRESHOLD,
	distance: 100,
	minMatchCharLength: 2,
	includeScore: true,
	includeMatches: true,
};

// Cached function to build search metadata and initial batch with Next.js cache
const getSearchMetadata = unstable_cache(
	async (): Promise<{
		products: Commerce.MappedProduct[];
		categories: string[];
		priceRange: { min: number; max: number };
	}> => {
		try {
			// Fetch a representative sample for metadata extraction
			const initialProducts = await safeProductBrowse({ first: METADATA_BATCH_SIZE });

			// If we have fewer than the batch size, use them all
			if (initialProducts.length < METADATA_BATCH_SIZE) {
				const metadata = extractMetadata(initialProducts);
				return {
					products: initialProducts,
					categories: metadata.categories,
					priceRange: metadata.priceRange,
				};
			}

			// For larger catalogs, fetch more products in batches to get complete metadata
			let allProducts = [...initialProducts];
			let hasMore = true;
			let currentOffset = METADATA_BATCH_SIZE;

			// Fetch up to maximum products for metadata
			while (hasMore && allProducts.length < MAX_METADATA_PRODUCTS) {
				const nextBatch = await safeProductBrowse({
					first: METADATA_BATCH_SIZE,
					offset: currentOffset,
				});

				if (nextBatch.length === 0) {
					hasMore = false;
				} else {
					allProducts.push(...nextBatch);
					currentOffset += METADATA_BATCH_SIZE;
				}
			}

			const metadata = extractMetadata(allProducts);
			return {
				products: allProducts,
				categories: metadata.categories,
				priceRange: metadata.priceRange,
			};
		} catch (error) {
			console.error("Failed to fetch products for search metadata:", error);
			const errorMessage = error instanceof Error ? error.message : "Unknown error";
			throw new Error(`Product fetch failed: ${errorMessage}`);
		}
	},
	["search-metadata"],
	{
		tags: ["products", "search"],
		revalidate: CACHE_TTL,
	},
);

async function performLiveSearch(
	query: string,
	filters?: NormalSearchFilters,
	limit = 20,
	offset = 0,
): Promise<{
	products: Commerce.MappedProduct[];
	totalCount: number;
}> {
	try {
		// For live search, we fetch products in batches and filter
		let allFilteredProducts: Commerce.MappedProduct[] = [];
		let hasMore = true;
		let currentOffset = 0;

		// Continue fetching until we have enough results or no more products
		while (hasMore && allFilteredProducts.length < offset + limit && currentOffset < LIVE_SEARCH_MAX_OFFSET) {
			const browseOptions: Parameters<typeof safeProductBrowse>[0] = {
				first: LIVE_SEARCH_BATCH_SIZE,
				offset: currentOffset,
			};

			// Add category filter if specified
			if (filters?.category) {
				browseOptions.filter = { category: filters.category };
			}

			const batch = await safeProductBrowse(browseOptions);

			if (batch.length === 0) {
				hasMore = false;
				break;
			}

			// Apply text search and remaining filters
			let filteredBatch = batch;

			// Text search using simple string matching for live search
			if (query.trim()) {
				const searchTerm = query.toLowerCase();
				filteredBatch = batch.filter(
					(product) =>
						product.name.toLowerCase().includes(searchTerm) ||
						product.description?.toLowerCase().includes(searchTerm) ||
						product.metadata?.category?.toLowerCase().includes(searchTerm) ||
						product.metadata?.variant?.toLowerCase().includes(searchTerm),
				);
			}

			// Apply price filter
			if (filters?.priceRange) {
				filteredBatch = filteredBatch.filter((product) => {
					const priceInCents = product.default_price.unit_amount || 0;
					const priceInDollars = priceInCents / 100;
					return (
						priceInDollars >= (filters.priceRange?.min || 0) &&
						priceInDollars <= (filters.priceRange?.max || Infinity)
					);
				});
			}

			allFilteredProducts.push(...filteredBatch);
			currentOffset += LIVE_SEARCH_BATCH_SIZE;
		}

		// Sort results
		const sortedProducts = sortProducts(allFilteredProducts, filters?.sortBy);

		return {
			products: sortedProducts.slice(offset, offset + limit),
			totalCount: sortedProducts.length,
		};
	} catch (error) {
		console.error("Live search failed:", error);
		throw error;
	}
}

function applyFilters(
	products: Commerce.MappedProduct[],
	filters?: NormalSearchFilters,
): Commerce.MappedProduct[] {
	if (!filters) return products;

	let filtered = products;

	// Category filter
	if (filters.category) {
		filtered = filtered.filter(
			(product) => product.metadata?.category?.toLowerCase() === filters.category?.toLowerCase(),
		);
	}

	// Price range filter
	if (filters.priceRange) {
		filtered = filtered.filter((product) => {
			const priceInCents = product.default_price.unit_amount || 0;
			const priceInDollars = priceInCents / 100;
			return (
				priceInDollars >= (filters.priceRange?.min || 0) &&
				priceInDollars <= (filters.priceRange?.max || Infinity)
			);
		});
	}

	return filtered;
}

function sortProducts(
	products: Commerce.MappedProduct[],
	sortBy?: NormalSearchFilters["sortBy"],
): Commerce.MappedProduct[] {
	if (!sortBy || sortBy === "relevance") return products;

	return [...products].sort((a, b) => {
		switch (sortBy) {
			case "price-asc":
				return (a.default_price.unit_amount || 0) - (b.default_price.unit_amount || 0);
			case "price-desc":
				return (b.default_price.unit_amount || 0) - (a.default_price.unit_amount || 0);
			case "name":
				return a.name.localeCompare(b.name);
			case "newest":
				return new Date(b.created).getTime() - new Date(a.created).getTime();
			default:
				return 0;
		}
	});
}

function extractMetadata(products: Commerce.MappedProduct[]) {
	const categories = new Set<string>();
	let minPrice = Infinity;
	let maxPrice = 0;

	products.forEach((product) => {
		if (product.metadata?.category) {
			categories.add(product.metadata.category);
		}
		const price = (product.default_price.unit_amount || 0) / 100; // Convert from cents
		minPrice = Math.min(minPrice, price);
		maxPrice = Math.max(maxPrice, price);
	});

	return {
		categories: Array.from(categories),
		priceRange: {
			min: minPrice === Infinity ? 0 : minPrice,
			max: maxPrice,
		},
	};
}

export async function enhancedNormalSearch(options: NormalSearchOptions): Promise<NormalSearchResult> {
	const { query, filters, limit = 20, offset = 0 } = options;

	try {
		// Get cached metadata for filters and price ranges
		const metadata = await getSearchMetadata();

		// For small catalogs, use in-memory fuzzy search
		if (metadata.products.length <= SMALL_CATALOG_THRESHOLD) {
			const fuse = new Fuse(metadata.products, fuseOptions);
			let results: Commerce.MappedProduct[];

			if (query.trim()) {
				// Perform fuzzy search
				const fuseResults = fuse.search(query);
				results = fuseResults.map((result) => result.item);
			} else {
				// No query, return all products
				results = metadata.products;
			}

			// Apply filters
			const filteredResults = applyFilters(results, filters);

			// Sort results
			const sortedResults = sortProducts(filteredResults, filters?.sortBy);

			// Paginate
			const paginatedResults = sortedResults.slice(offset, offset + limit);

			return {
				products: paginatedResults,
				totalCount: sortedResults.length,
				categories: metadata.categories,
				priceRange: metadata.priceRange,
			};
		} else {
			// For large catalogs, use live search with streaming
			const searchResults = await performLiveSearch(query, filters, limit, offset);

			return {
				products: searchResults.products,
				totalCount: searchResults.totalCount,
				categories: metadata.categories,
				priceRange: metadata.priceRange,
			};
		}
	} catch (error) {
		console.error("Enhanced normal search failed:", error);
		return {
			products: [],
			totalCount: 0,
			categories: [],
			priceRange: { min: 0, max: 0 },
			error: error instanceof Error ? error.message : "Search failed",
		};
	}
}
