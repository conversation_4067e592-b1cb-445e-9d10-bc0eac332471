import { cookies } from "next/headers";
import { safeJsonParse } from "@/lib/utils";

export const CART_COOKIE = "yns_cart";
export const CART_COOKIE_MAX_AGE = 30 * 24 * 60 * 60; // 30 days

export type CartCookieJson = { id: string; linesCount: number };

function getSecureCookieOptions() {
	return {
		httpOnly: false, // Cart needs to be accessible client-side for commerce-kit
		secure: process.env.NODE_ENV === "production",
		sameSite: "strict" as const,
		maxAge: CART_COOKIE_MAX_AGE,
		path: "/",
	};
}

export async function setCartCookieJson(cartCookieJson: CartCookieJson) {
	try {
		(await cookies()).set(CART_COOKIE, JSON.stringify(cartCookieJson), getSecureCookieOptions());
	} catch (error) {
		console.error("Failed to set cart cookie", error);
	}
}

export async function clearCartCookie(): Promise<void> {
	(await cookies()).set(CART_COOKIE, "", {
		maxAge: 0,
		secure: process.env.NODE_ENV === "production",
		sameSite: "strict",
		path: "/",
	});
}

export async function getCartCookieJson(): Promise<null | CartCookieJson> {
	const cookiesValue = await cookies();
	const cartCookieJson = safeJsonParse(cookiesValue.get(CART_COOKIE)?.value);

	if (
		!cartCookieJson ||
		typeof cartCookieJson !== "object" ||
		!("id" in cartCookieJson) ||
		!("linesCount" in cartCookieJson) ||
		typeof cartCookieJson.id !== "string" ||
		typeof cartCookieJson.linesCount !== "number"
	) {
		return null;
	}
	return cartCookieJson as CartCookieJson;
}
