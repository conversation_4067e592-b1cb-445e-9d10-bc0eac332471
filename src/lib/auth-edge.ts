import { type JWTPayload, jwtVerify, SignJWT } from "jose";
import { type NextRequest, NextResponse } from "next/server";
import { env } from "../env.mjs";

const key = new TextEncoder().encode(env.SECRET);
const SessionDuration = 24 * 60 * 60 * 1000;

interface User {
	email: string;
}

interface SessionData extends JWTPayload {
	user: User;
	expires: number;
}

function validateSessionData(data: unknown): SessionData | null {
	if (!data || typeof data !== "object") return null;
	const obj = data as Record<string, unknown>;

	if (!obj.user || typeof obj.user !== "object") return null;
	const user = obj.user as Record<string, unknown>;

	if (typeof user.email !== "string" || !user.email) return null;
	if (typeof obj.expires !== "number" || obj.expires <= 0) return null;

	return {
		user: { email: user.email },
		expires: obj.expires,
	};
}

export async function encrypt(payload: SessionData): Promise<string> {
	return await new SignJWT(payload)
		.setProtectedHeader({ alg: "HS256" })
		.setIssuedAt()
		.setExpirationTime(payload.expires)
		.sign(key);
}

export async function decrypt(input: string): Promise<SessionData | null> {
	try {
		const r = await jwtVerify(input, key, {
			algorithms: ["HS256"],
		});
		return validateSessionData(r.payload);
	} catch (e) {
		if (e instanceof Error) {
			console.log(e.message);
		}
		return null;
	}
}

export async function updateSession(request: NextRequest): Promise<NextResponse | undefined> {
	const session = request.cookies.get("session")?.value;
	if (!session) return;

	const data = await decrypt(session);
	if (!data) return;

	if (data.expires - Date.now() < 60 * 60 * 1000) {
		data.expires = Date.now() + SessionDuration;

		const res = NextResponse.next();
		res.cookies.set({
			name: "session",
			value: await encrypt(data),
			httpOnly: true,
			secure: process.env.NODE_ENV === "production",
			sameSite: "strict",
			expires: new Date(data.expires),
		});
		return res;
	}

	return NextResponse.next();
}
