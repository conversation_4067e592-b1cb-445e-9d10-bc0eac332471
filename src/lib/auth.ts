"use server";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { env } from "../env.mjs";
import { decrypt, encrypt } from "./auth-edge";
import { verifyPassword } from "./password";
import { isValidPasswordHash } from "./password-utils";

const SessionDuration = 24 * 60 * 60 * 1000;

function validateFormData(formData: FormData): { email: string; password: string } | null {
	const email = formData.get("email");
	const password = formData.get("password");

	if (typeof email !== "string" || !email.trim()) return null;
	if (typeof password !== "string" || !password.trim()) return null;

	return { email: email.trim(), password };
}

export async function login(_state: unknown, formData: FormData): Promise<{ error?: string } | undefined> {
	"use server";

	const credentials = validateFormData(formData);
	if (!credentials) {
		return { error: "Invalid form data" };
	}

	const { email, password } = credentials;

	if (email !== env.EMAIL) {
		return { error: "Invalid credentials" };
	}

	// Ensure password is a bcrypt hash for security
	if (!isValidPasswordHash(env.PASSWORD)) {
		console.error("PASSWORD must be a bcrypt hash for security");
		return { error: "Invalid credentials" };
	}

	const isPasswordValid = await verifyPassword(password, env.PASSWORD);

	if (!isPasswordValid) {
		return { error: "Invalid credentials" };
	}

	const expires = Date.now() + SessionDuration;
	const session = await encrypt({ user: { email }, expires });

	(await cookies()).set("session", session, {
		expires: new Date(expires),
		httpOnly: true,
		secure: process.env.NODE_ENV === "production",
		sameSite: "strict",
	});

	redirect("/orders");
	return;
}

export async function logout() {
	"use server";
	(await cookies()).delete("session");
	redirect("/login");
}

export async function auth() {
	const session = (await cookies()).get("session")?.value;
	if (!session) return null;

	const data = await decrypt(session);
	if (!data || data.expires < Date.now()) {
		(await cookies()).delete("session");
		return null;
	}

	return data;
}
