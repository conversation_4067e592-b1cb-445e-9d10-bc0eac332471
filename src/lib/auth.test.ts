import type { RequestCookie } from "next/dist/compiled/@edge-runtime/cookies";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { NextRequest, NextResponse } from "next/server";
import { beforeEach, describe, expect, it, vi } from "vitest";

// Mock env before importing auth
vi.mock("../env.mjs", () => ({
	env: {
		SECRET: "12345678901234567890123456789012", // Exactly 32 characters
		EMAIL: "<EMAIL>",
		PASSWORD: "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBcQGQ9xPWvHDe",
	},
}));

import { auth, login, logout } from "./auth";
import { decrypt, encrypt, updateSession } from "./auth-edge";

// Mock password utilities
vi.mock("./password", () => ({
	verifyPassword: vi.fn(),
}));

vi.mock("./password-utils", () => ({
	isValidPasswordHash: vi.fn(),
}));

const mockRedirect = vi.mocked(redirect);
const mockCookies = vi.mocked(cookies);

// Import the actual types we need
import type { RequestCookies, ResponseCookies } from "next/dist/compiled/@edge-runtime/cookies";

// Create properly typed mock that satisfies Next.js cookie interface
type MockCookieStore = Omit<RequestCookies, "set" | "clear" | "delete"> &
	Pick<ResponseCookies, "set" | "delete">;

// Mock cookie methods with proper typing
const mockCookieGet = vi.fn<(name: string) => RequestCookie | undefined>();
const mockCookieSet = vi.fn<(name: string, value: string, options?: object) => ResponseCookies>();
const mockCookieDelete = vi.fn<(name: string) => ResponseCookies>();

// Import and mock password functions
import { verifyPassword } from "./password";
import { isValidPasswordHash } from "./password-utils";

const mockVerifyPassword = vi.mocked(verifyPassword);
const mockIsValidPasswordHash = vi.mocked(isValidPasswordHash);

describe("Authentication", () => {
	beforeEach(() => {
		vi.clearAllMocks();

		// Create a Map-like mock that satisfies the RequestCookies interface
		const cookieMap = new Map<string, RequestCookie>();

		const mockCookieStore = {
			get: vi.fn((name: string) => cookieMap.get(name)),
			set: vi.fn().mockReturnThis(),
			delete: vi.fn().mockReturnThis(),
			getAll: vi.fn(() => Array.from(cookieMap.values())),
			has: vi.fn((name: string) => cookieMap.has(name)),
			size: 0,
			[Symbol.iterator]: () => cookieMap[Symbol.iterator](),
		} as MockCookieStore;

		// Configure mocks to use the same functions
		mockCookieGet.mockImplementation((name: string) => cookieMap.get(name));
		mockCookieSet.mockReturnValue(mockCookieStore as ResponseCookies);
		mockCookieDelete.mockReturnValue(mockCookieStore as ResponseCookies);

		mockCookies.mockResolvedValue(mockCookieStore);
	});

	describe("encrypt and decrypt", () => {
		it("should encrypt and decrypt session data", async () => {
			const sessionData = {
				user: { email: "<EMAIL>" },
				expires: Date.now() + 1000000,
			};

			const encrypted = await encrypt(sessionData);
			expect(encrypted).toBeTruthy();
			expect(typeof encrypted).toBe("string");

			const decrypted = await decrypt(encrypted);
			expect(decrypted).toEqual(
				expect.objectContaining({
					user: { email: "<EMAIL>" },
					expires: expect.any(Number),
				}),
			);
		});

		it("should return null for invalid token", async () => {
			const result = await decrypt("invalid-token");
			expect(result).toBeNull();
		});

		it("should return null for empty token", async () => {
			const result = await decrypt("");
			expect(result).toBeNull();
		});
	});

	describe("login", () => {
		it("should reject login when password is not bcrypt hash", async () => {
			mockIsValidPasswordHash.mockReturnValue(false);

			const formData = new FormData();
			formData.append("email", "<EMAIL>");
			formData.append("password", "test-password");

			const result = await login(null, formData);

			expect(result).toEqual({ error: "Invalid credentials" });
			expect(mockCookieSet).not.toHaveBeenCalled();
			expect(mockRedirect).not.toHaveBeenCalled();
		});

		it("should login with valid bcrypt password", async () => {
			mockIsValidPasswordHash.mockReturnValue(true);
			mockVerifyPassword.mockResolvedValue(true);

			const formData = new FormData();
			formData.append("email", "<EMAIL>");
			formData.append("password", "test-password");

			const result = await login(null, formData);

			expect(result).toBeUndefined();
			expect(mockVerifyPassword).toHaveBeenCalledWith(
				"test-password",
				"$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBcQGQ9xPWvHDe",
			);
			expect(mockRedirect).toHaveBeenCalledWith("/orders");
		});

		it("should reject invalid email", async () => {
			const formData = new FormData();
			formData.append("email", "<EMAIL>");
			formData.append("password", "test-password");

			const result = await login(null, formData);

			expect(result).toEqual({ error: "Invalid credentials" });
			expect(mockCookieSet).not.toHaveBeenCalled();
			expect(mockRedirect).not.toHaveBeenCalled();
		});

		it("should reject invalid password with bcrypt", async () => {
			mockIsValidPasswordHash.mockReturnValue(true);
			mockVerifyPassword.mockResolvedValue(false);

			const formData = new FormData();
			formData.append("email", "<EMAIL>");
			formData.append("password", "wrong-password");

			const result = await login(null, formData);

			expect(result).toEqual({ error: "Invalid credentials" });
			expect(mockCookieSet).not.toHaveBeenCalled();
		});

		it("should reject login when password hash validation fails", async () => {
			mockIsValidPasswordHash.mockReturnValue(false);

			const formData = new FormData();
			formData.append("email", "<EMAIL>");
			formData.append("password", "wrong-password");

			const result = await login(null, formData);

			expect(result).toEqual({ error: "Invalid credentials" });
			expect(mockCookieSet).not.toHaveBeenCalled();
			expect(mockRedirect).not.toHaveBeenCalled();
		});

		it("should reject missing email", async () => {
			const formData = new FormData();
			formData.append("password", "test-password");

			const result = await login(null, formData);

			expect(result).toEqual({ error: "Invalid form data" });
		});

		it("should reject missing password", async () => {
			const formData = new FormData();
			formData.append("email", "<EMAIL>");

			const result = await login(null, formData);

			expect(result).toEqual({ error: "Invalid form data" });
		});

		it("should reject empty email", async () => {
			const formData = new FormData();
			formData.append("email", "");
			formData.append("password", "test-password");

			const result = await login(null, formData);

			expect(result).toEqual({ error: "Invalid form data" });
		});

		it("should reject empty password", async () => {
			const formData = new FormData();
			formData.append("email", "<EMAIL>");
			formData.append("password", "");

			const result = await login(null, formData);

			expect(result).toEqual({ error: "Invalid form data" });
		});

		it("should handle non-string form data", async () => {
			const formData = new FormData();
			const file = new File(["test"], "test.txt");
			// Test with File object instead of string to simulate form data edge case
			formData.set("email", file as unknown as string);
			formData.append("password", "test-password");

			const result = await login(null, formData);

			expect(result).toEqual({ error: "Invalid form data" });
		});
	});

	describe("logout", () => {
		it("should clear session cookie and redirect", async () => {
			await logout();

			expect(mockCookieDelete).toHaveBeenCalledWith("session");
			expect(mockRedirect).toHaveBeenCalledWith("/login");
		});
	});

	describe("auth", () => {
		it("should return session data for valid session", async () => {
			const sessionData = {
				user: { email: "<EMAIL>" },
				expires: Date.now() + 1000000,
			};

			const token = await encrypt(sessionData);
			mockCookieGet.mockReturnValue({ name: "session", value: token });

			const result = await auth();

			expect(result).toEqual(
				expect.objectContaining({
					user: { email: "<EMAIL>" },
					expires: expect.any(Number),
				}),
			);
		});

		it("should return null when no session cookie", async () => {
			mockCookieGet.mockReturnValue(undefined);

			const result = await auth();

			expect(result).toBeNull();
		});

		it("should return null and clear cookie for expired session", async () => {
			const sessionData = {
				user: { email: "<EMAIL>" },
				expires: Date.now() - 1000, // Expired
			};

			const token = await encrypt(sessionData);
			mockCookieGet.mockReturnValue({ name: "session", value: token });

			const result = await auth();

			expect(result).toBeNull();
			expect(mockCookieDelete).toHaveBeenCalledWith("session");
		});

		it("should return null for invalid session token", async () => {
			mockCookieGet.mockReturnValue({ name: "session", value: "invalid-token" });

			const result = await auth();

			expect(result).toBeNull();
		});
	});

	describe("updateSession", () => {
		it("should update session when close to expiry", async () => {
			const sessionData = {
				user: { email: "<EMAIL>" },
				expires: Date.now() + 30 * 60 * 1000, // 30 minutes from now
			};

			const token = await encrypt(sessionData);

			// Mock request with cookie
			const request = new NextRequest("http://localhost:3000");
			vi.spyOn(request.cookies, "get").mockReturnValue({ name: "session", value: token });

			const result = await updateSession(request);

			expect(result).toBeInstanceOf(NextResponse);
			// Session should be updated with new expiry
		});

		it("should not update session when not close to expiry", async () => {
			const sessionData = {
				user: { email: "<EMAIL>" },
				expires: Date.now() + 2 * 60 * 60 * 1000, // 2 hours from now
			};

			const token = await encrypt(sessionData);

			// Mock request with cookie
			const request = new NextRequest("http://localhost:3000");
			vi.spyOn(request.cookies, "get").mockReturnValue({ name: "session", value: token });

			const result = await updateSession(request);

			expect(result).toBeInstanceOf(NextResponse);
		});

		it("should return undefined when no session", async () => {
			const request = new NextRequest("http://localhost:3000");
			vi.spyOn(request.cookies, "get").mockReturnValue(undefined);

			const result = await updateSession(request);

			expect(result).toBeUndefined();
		});

		it("should return undefined for invalid session", async () => {
			const request = new NextRequest("http://localhost:3000");
			vi.spyOn(request.cookies, "get").mockReturnValue({ name: "session", value: "invalid-token" });

			const result = await updateSession(request);

			expect(result).toBeUndefined();
		});
	});
});
