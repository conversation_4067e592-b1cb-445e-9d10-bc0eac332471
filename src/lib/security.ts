import type { NextRequest } from "next/server";

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute sliding window for rate limiting
const RATE_LIMIT_MAX_REQUESTS = 10; // Maximum requests per IP within the time window

interface RateLimitRecord {
	count: number;
	resetTime: number;
}

const rateLimitMap = new Map<string, RateLimitRecord>();

export function checkRateLimit(ip: string): boolean {
	const now = Date.now();
	const record = rateLimitMap.get(ip);

	if (!record || now > record.resetTime) {
		rateLimitMap.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
		return true;
	}

	if (record.count >= RATE_LIMIT_MAX_REQUESTS) {
		return false;
	}

	record.count++;
	return true;
}

export function getClientIP(request: NextRequest): string {
	const forwarded = request.headers.get("x-forwarded-for");
	const realIP = request.headers.get("x-real-ip");
	const ip = forwarded?.split(",")[0] || realIP || "unknown";
	return ip.trim();
}

export function validateOrigin(request: NextRequest): boolean {
	const origin = request.headers.get("origin");
	const referer = request.headers.get("referer");
	const host = request.headers.get("host");

	if (!origin && !referer) {
		return false;
	}

	const allowedOrigins = [
		`https://${host}`,
		`http://${host}`,
		...(process.env.NODE_ENV === "development" ? ["http://localhost:3000"] : []),
	];

	const requestOrigin = origin || (referer ? new URL(referer).origin : null);

	return requestOrigin ? allowedOrigins.includes(requestOrigin) : false;
}

export function validateContentType(request: NextRequest, expectedType: string): boolean {
	const contentType = request.headers.get("content-type");
	return contentType?.includes(expectedType) ?? false;
}

export function sanitizeInput(input: unknown): string {
	if (typeof input !== "string") {
		return "";
	}

	return input
		.trim()
		.replace(/[<>'"&]/g, "") // Remove potentially dangerous HTML characters
		.slice(0, 1000); // Limit input length to prevent memory issues
}

export function createSecurityHeaders() {
	return {
		"X-Content-Type-Options": "nosniff",
		"X-Frame-Options": "DENY",
		"X-XSS-Protection": "1; mode=block",
		"Referrer-Policy": "strict-origin-when-cross-origin",
	};
}

export function clearRateLimit(ip?: string): void {
	if (ip) {
		rateLimitMap.delete(ip);
	} else {
		rateLimitMap.clear();
	}
}
