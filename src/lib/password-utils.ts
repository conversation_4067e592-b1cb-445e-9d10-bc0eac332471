interface PasswordRequirements {
	minLength: number;
	requireUppercase: boolean;
	requireLowercase: boolean;
	requireNumbers: boolean;
	requireSpecialChars: boolean;
}

const DEFAULT_REQUIREMENTS: PasswordRequirements = {
	minLength: 8,
	requireUppercase: true,
	requireLowercase: true,
	requireNumbers: true,
	requireSpecialChars: false,
};

export function validatePasswordRequirements(
	password: string,
	requirements: PasswordRequirements = DEFAULT_REQUIREMENTS,
): { valid: boolean; errors: string[] } {
	const errors: string[] = [];

	if (password.length < requirements.minLength) {
		errors.push(`Password must be at least ${requirements.minLength} characters long`);
	}

	if (requirements.requireUppercase && !/[A-Z]/.test(password)) {
		errors.push("Password must contain at least one uppercase letter");
	}

	if (requirements.requireLowercase && !/[a-z]/.test(password)) {
		errors.push("Password must contain at least one lowercase letter");
	}

	if (requirements.requireNumbers && !/\d/.test(password)) {
		errors.push("Password must contain at least one number");
	}

	if (requirements.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
		errors.push("Password must contain at least one special character");
	}

	return {
		valid: errors.length === 0,
		errors,
	};
}

export function isValidPasswordHash(hash: string): boolean {
	return hash.startsWith("$2") && hash.length >= 59;
}

export type { PasswordRequirements };
export { DEFAULT_REQUIREMENTS };
