import type { StaticImageData } from "next/image";
import AccessoriesImage from "@/images/accessories.jpg";
import ApparelImage from "@/images/apparel.jpg";

export interface Category {
	name: string;
	slug: string;
	image: StaticImageData;
	description?: string;
	seoTitle?: string;
	seoDescription?: string;
	order: number;
	isVisible: boolean;
	color?: string;
}

export const config = {
	categories: [
		{
			name: "Apparel",
			slug: "apparel",
			image: ApparelImage,
			description: "Clothing and fashion items for every style",
			seoTitle: "Premium Apparel Collection",
			seoDescription:
				"Discover our curated selection of high-quality clothing and fashion items. From everyday essentials to statement pieces.",
			order: 1,
			isVisible: true,
			color: "#6366F1",
		},
		{
			name: "Accessories",
			slug: "accessories",
			image: AccessoriesImage,
			description: "Complete your look with our stylish accessories",
			seoTitle: "Fashion Accessories & More",
			seoDescription:
				"Elevate your style with our collection of premium accessories. From jewelry to bags, find the perfect finishing touches.",
			order: 2,
			isVisible: true,
			color: "#EC4899",
		},
	] as const satisfies readonly Category[],

	social: {
		x: "https://x.com/yourstore",
		facebook: "https://facebook.com/yourstore",
	},

	contact: {
		email: "<EMAIL>",
		phone: "+****************",
		address: "123 Store Street, City, Country",
	},
};

export type StoreConfig = typeof config;
export default config;
