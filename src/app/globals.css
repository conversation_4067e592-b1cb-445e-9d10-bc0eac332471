@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme {
	--color-border: hsl(var(--border));
	--color-input: hsl(var(--input));
	--color-ring: hsl(var(--ring));
	--color-background: hsl(var(--background));
	--color-foreground: hsl(var(--foreground));

	--color-primary: hsl(var(--primary));
	--color-primary-foreground: hsl(var(--primary-foreground));

	--color-secondary: hsl(var(--secondary));
	--color-secondary-foreground: hsl(var(--secondary-foreground));

	--color-destructive: hsl(var(--destructive));
	--color-destructive-foreground: hsl(var(--destructive-foreground));

	--color-muted: hsl(var(--muted));
	--color-muted-foreground: hsl(var(--muted-foreground));

	--color-accent: hsl(var(--accent));
	--color-accent-foreground: hsl(var(--accent-foreground));

	--color-popover: hsl(var(--popover));
	--color-popover-foreground: hsl(var(--popover-foreground));

	--color-card: hsl(var(--card));
	--color-card-foreground: hsl(var(--card-foreground));

	--radius-lg: var(--radius);
	--radius-md: calc(var(--radius) - 2px);
	--radius-sm: calc(var(--radius) - 4px);

	--animate-accordion-down: accordion-down 0.2s ease-out;
	--animate-accordion-up: accordion-up 0.2s ease-out;

	--breakpoint-xs: 400px;
	--breakpoint-smb: 720px;

	@keyframes accordion-down {
		from {
			height: 0;
		}
		to {
			height: var(--radix-accordion-content-height);
		}
	}
	@keyframes accordion-up {
		from {
			height: var(--radix-accordion-content-height);
		}
		to {
			height: 0;
		}
	}
}

@utility container {
	margin-inline: auto;
	padding-inline: 2rem;
	@media (width >= --theme(--breakpoint-xs)) {
		max-width: none;
	}
	@media (width >= 1400px) {
		max-width: 1400px;
	}
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}
}

@utility text-pretty {
	text-wrap: balance;
	text-wrap: pretty;
}

@utility animation-fade-in {
	animation: fade-in 0.2s ease-out forwards;
}

@utility animation-slide-from-right {
	animation: slide-from-right 0.2s ease-out forwards;
}

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 222.2 84% 4.9%;

		--card: 0 0% 100%;
		--card-foreground: 222.2 84% 4.9%;

		--popover: 0 0% 100%;
		--popover-foreground: 222.2 84% 4.9%;

		--primary: 222.2 47.4% 11.2%;
		--primary-foreground: 210 40% 98%;

		--secondary: 210 40% 96.1%;
		--secondary-foreground: 222.2 47.4% 11.2%;

		--muted: 210 40% 96.1%;
		--muted-foreground: 215.4 16.3% 46.9%;

		--accent: 210 40% 96.1%;
		--accent-foreground: 222.2 47.4% 11.2%;

		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 210 40% 98%;

		--border: 214.3 31.8% 91.4%;
		--input: 214.3 31.8% 91.4%;
		--ring: 222.2 84% 4.9%;

		--radius: 0.5rem;
	}

	.dark {
		--background: 222.2 84% 4.9%;
		--foreground: 210 40% 98%;

		--card: 222.2 84% 4.9%;
		--card-foreground: 210 40% 98%;

		--popover: 222.2 84% 4.9%;
		--popover-foreground: 210 40% 98%;

		--primary: 210 40% 98%;
		--primary-foreground: 222.2 47.4% 11.2%;

		--secondary: 217.2 32.6% 17.5%;
		--secondary-foreground: 210 40% 98%;

		--muted: 217.2 32.6% 17.5%;
		--muted-foreground: 215 20.2% 65.1%;

		--accent: 217.2 32.6% 17.5%;
		--accent-foreground: 210 40% 98%;

		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 210 40% 98%;

		--border: 217.2 32.6% 17.5%;
		--input: 217.2 32.6% 17.5%;
		--ring: 212.7 26.8% 83.9%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}

label:has([required]) > span:first-child {
	&::after {
		content: "*";
		@apply ml-1 text-destructive;
	}
}

.CollapsibleContent {
	overflow: hidden;
}
.CollapsibleContent[data-state="open"] {
	animation: slideDown 250ms ease-in-out;
}
.CollapsibleContent[data-state="closed"] {
	animation: slideUp 250ms ease-in-out;
}

input[type="search" i]::-webkit-search-cancel-button {
	display: none;
}

@keyframes slideDown {
	from {
		height: 0;
	}
	to {
		height: var(--radix-collapsible-content-height);
	}
}

@keyframes slideUp {
	from {
		height: var(--radix-collapsible-content-height);
	}
	to {
		height: 0;
	}
}

@keyframes fade-in {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slide-from-right {
	from {
		transform: translateX(100%);
	}
	to {
		transform: translateX(0);
	}
}

.nav-border-reveal::after {
	@apply content-[''] w-full h-px absolute bottom-0 bg-neutral-200;

	animation: nav-border-reveal;
	animation-timeline: scroll();
	animation-range: 0 100px;
}

@keyframes nav-border-reveal {
	0% {
		opacity: 0%;
	}
	100% {
		opacity: 100%;
	}
}
