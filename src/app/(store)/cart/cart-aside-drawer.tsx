"use client";

import FocusTrap from "focus-trap-react";
import type { ReactNode } from "react";
import { Drawer, DrawerContent, DrawerTitle } from "@/components/ui/drawer";
import { useMediaQuery } from "@/components/ui/hooks/use-media-query";
import { useCartModal } from "@/context/cart-modal";

export const CartAsideDrawer = ({ children }: { children: ReactNode }) => {
	const { open, setOpen } = useCartModal();

	const isDesktop = useMediaQuery("(min-width: 640px)");

	return (
		<Drawer open={open} shouldScaleBackground={true} direction={isDesktop ? "right" : "bottom"}>
			<DrawerTitle className="sr-only">Shopping cart</DrawerTitle>
			<DrawerContent
				className="sm:fixed sm:bottom-0 sm:left-auto sm:right-0 sm:top-0 sm:mt-0 sm:flex sm:h-full sm:w-1/2 sm:flex-col sm:overflow-hidden sm:rounded-none sm:bg-white sm:shadow-xl lg:w-1/3"
				aria-describedby="cart-overlay-description"
				onPointerDownOutside={() => {
					setOpen(false);
				}}
				onEscapeKeyDown={() => {
					setOpen(false);
				}}
			>
				<FocusTrap
					active={open}
					focusTrapOptions={{
						initialFocus: () => {
							const firstButton = document.querySelector('[data-testid="cart-modal"] button');
							if (firstButton) {
								return firstButton as HTMLElement;
							}
							const modalContainer = document.querySelector('[data-testid="cart-modal"]');
							return modalContainer as HTMLElement;
						},
						returnFocusOnDeactivate: true,
						escapeDeactivates: false,
					}}
				>
					<div data-testid="cart-modal" tabIndex={-1}>
						{children}
					</div>
				</FocusTrap>
			</DrawerContent>
		</Drawer>
	);
};
