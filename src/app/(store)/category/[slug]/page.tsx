import type { Metadata } from "next/types";
import { publicUrl } from "@/env.mjs";
import { getTranslations } from "@/i18n/server";
import { getCategoryWithProducts } from "@/lib/categories";
import { ProductList } from "@/ui/products/product-list";

export const generateMetadata = async (props: { params: Promise<{ slug: string }> }): Promise<Metadata> => {
	const params = await props.params;
	const { category } = await getCategoryWithProducts(params.slug);
	const t = await getTranslations("/category.metadata");

	return {
		title: category.seoTitle || t("title", { categoryName: category.name }),
		description: category.seoDescription,
		alternates: { canonical: `${publicUrl}/category/${params.slug}` },
	};
};

export default async function CategoryPage(props: { params: Promise<{ slug: string }> }) {
	const params = await props.params;
	const { category, products } = await getCategoryWithProducts(params.slug);

	return (
		<main className="pb-8">
			<div className="mb-6">
				<h1 className="text-3xl font-bold leading-none tracking-tight text-foreground">{category.name}</h1>
				{category.description && <p className="mt-2 text-lg text-muted-foreground">{category.description}</p>}
			</div>
			<ProductList products={products} />
		</main>
	);
}
