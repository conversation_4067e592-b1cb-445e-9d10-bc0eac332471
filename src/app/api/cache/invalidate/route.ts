import { type NextRequest, NextResponse } from "next/server";
import {
	type CacheInvalidationOptions,
	invalidateAllCaches,
	invalidateCache,
	invalidateProductsCache,
	invalidateSearchCache,
} from "@/lib/cache-invalidation";
import { checkRateLimit, createSecurityHeaders, getClientIP, validateOrigin } from "@/lib/security";

export async function POST(request: NextRequest) {
	try {
		const ip = getClientIP(request);

		if (!checkRateLimit(ip)) {
			return NextResponse.json(
				{ error: "Too many requests. Please try again later." },
				{
					status: 429,
					headers: {
						...createSecurityHeaders(),
						"Retry-After": "60",
					},
				},
			);
		}

		if (!validateOrigin(request)) {
			return NextResponse.json(
				{ error: "Invalid request origin" },
				{
					status: 403,
					headers: createSecurityHeaders(),
				},
			);
		}

		const body = await request.json().catch(() => null);

		if (!body || typeof body !== "object") {
			return NextResponse.json(
				{ error: "Invalid request body" },
				{
					status: 400,
					headers: createSecurityHeaders(),
				},
			);
		}

		const { type, options } = body as {
			type?: "search" | "products" | "all" | "selective";
			options?: CacheInvalidationOptions;
		};

		switch (type) {
			case "search":
				invalidateSearchCache();
				break;
			case "products":
				invalidateProductsCache();
				break;
			case "all":
				invalidateAllCaches();
				break;
			case "selective":
				if (!options) {
					return NextResponse.json(
						{ error: "Options required for selective cache invalidation" },
						{
							status: 400,
							headers: createSecurityHeaders(),
						},
					);
				}
				invalidateCache(options);
				break;
			default:
				return NextResponse.json(
					{ error: "Invalid cache invalidation type. Use: search, products, all, or selective" },
					{
						status: 400,
						headers: createSecurityHeaders(),
					},
				);
		}

		return NextResponse.json(
			{
				success: true,
				message: `Cache invalidation completed for type: ${type}`,
				timestamp: new Date().toISOString(),
			},
			{
				status: 200,
				headers: createSecurityHeaders(),
			},
		);
	} catch (error) {
		console.error("Cache invalidation API error:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{
				status: 500,
				headers: createSecurityHeaders(),
			},
		);
	}
}

export async function GET(request: NextRequest) {
	return NextResponse.json(
		{
			message: "Cache invalidation API endpoint",
			usage: {
				method: "POST",
				body: {
					type: "search | products | all | selective",
					options: "Required for selective type: { products?: boolean, search?: boolean, cart?: boolean }",
				},
			},
		},
		{
			status: 200,
			headers: createSecurityHeaders(),
		},
	);
}
