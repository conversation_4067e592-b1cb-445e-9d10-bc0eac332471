// Import Stripe type for proper typing
import type Stripe from "stripe";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { POST } from "./route";

// Define minimal Stripe interfaces for testing
interface MockStripeProvider {
	webhooks: {
		constructEventAsync: ReturnType<typeof vi.fn>;
	};
	tax?: {
		transactions: {
			createFromCalculation: ReturnType<typeof vi.fn>;
		};
	};
	products?: {
		update: ReturnType<typeof vi.fn>;
	};
}

// Mock dependencies
vi.mock("commerce-kit", () => ({
	provider: vi.fn(),
	getProductsFromMetadata: vi.fn(),
}));

vi.mock("commerce-kit/internal", () => ({
	cartMetadataSchema: {
		parse: vi.fn(),
	},
}));

vi.mock("next/cache", () => ({
	revalidateTag: vi.fn(),
}));

vi.mock("@/env.mjs", () => ({
	env: {
		STRIPE_WEBHOOK_SECRET: "whsec_test_secret",
	},
}));

vi.mock("@/lib/cache-invalidation", () => ({
	invalidateSearchCache: vi.fn(),
}));

vi.mock("@/lib/utils", () => ({
	unpackPromise: vi.fn(),
}));

describe("/api/stripe-webhook", () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	describe("POST", () => {
		it("should return 500 when STRIPE_WEBHOOK_SECRET is not configured", async () => {
			// Mock missing webhook secret
			vi.doMock("@/env.mjs", () => ({
				env: {
					STRIPE_WEBHOOK_SECRET: undefined,
				},
			}));

			const request = new Request("http://localhost:3000/api/stripe-webhook", {
				method: "POST",
				body: "test body",
				headers: {
					"Stripe-Signature": "test-signature",
				},
			});

			const response = await POST(request);

			expect(response.status).toBe(500);
			const text = await response.text();
			expect(text).toBe("STRIPE_WEBHOOK_SECRET is not configured");
		});

		it("should return 401 when no signature provided", async () => {
			const request = new Request("http://localhost:3000/api/stripe-webhook", {
				method: "POST",
				body: "test body",
			});

			const response = await POST(request);

			expect(response.status).toBe(401);
			const text = await response.text();
			expect(text).toBe("No signature");
		});

		it("should return 401 when signature is invalid", async () => {
			const { unpackPromise } = await import("@/lib/utils");
			vi.mocked(unpackPromise).mockResolvedValue([new Error("Invalid signature"), null]);

			const mockStripe: MockStripeProvider = {
				webhooks: {
					constructEventAsync: vi.fn(),
				},
			};

			const { provider } = await import("commerce-kit");
			vi.mocked(provider).mockReturnValue(mockStripe as unknown as Stripe);

			const request = new Request("http://localhost:3000/api/stripe-webhook", {
				method: "POST",
				body: "test body",
				headers: {
					"Stripe-Signature": "invalid-signature",
				},
			});

			const response = await POST(request);

			expect(response.status).toBe(401);
			const text = await response.text();
			expect(text).toBe("Invalid signature");
		});

		it("should handle product.created event", async () => {
			const mockEvent = {
				type: "product.created",
				data: {
					object: {
						id: "prod_123",
					},
				},
			};

			const { unpackPromise } = await import("@/lib/utils");
			vi.mocked(unpackPromise).mockResolvedValue([null, mockEvent]);

			const mockStripe: MockStripeProvider = {
				webhooks: {
					constructEventAsync: vi.fn(),
				},
			};

			const { provider } = await import("commerce-kit");
			vi.mocked(provider).mockReturnValue(mockStripe as unknown as Stripe);

			const { invalidateSearchCache } = await import("@/lib/cache-invalidation");
			const invalidateSpy = vi.mocked(invalidateSearchCache);

			const request = new Request("http://localhost:3000/api/stripe-webhook", {
				method: "POST",
				body: JSON.stringify(mockEvent),
				headers: {
					"Stripe-Signature": "valid-signature",
				},
			});

			const response = await POST(request);

			expect(response.status).toBe(200);
			const json = await response.json();
			expect(json).toEqual({ received: true });
			expect(invalidateSpy).toHaveBeenCalled();
		});

		it("should handle payment_intent.succeeded event", async () => {
			const mockMetadata = {
				taxCalculationId: "taxcalc_123",
			};

			const mockEvent = {
				type: "payment_intent.succeeded",
				data: {
					object: {
						id: "pi_123",
						metadata: mockMetadata,
					},
				},
			};

			const mockProduct = {
				id: "prod_123",
				metadata: {
					slug: "test-product",
					stock: 5,
				},
				default_price: {
					id: "price_123",
					object: "price" as const,
					active: true,
					billing_scheme: "per_unit" as const,
					created: **********,
					currency: "usd",
					custom_unit_amount: null,
					livemode: false,
					lookup_key: null,
					metadata: {},
					nickname: null,
					product: "prod_123",
					recurring: null,
					tax_behavior: null,
					tiers_mode: null,
					transform_quantity: null,
					type: "one_time" as const,
					unit_amount: 2000,
					unit_amount_decimal: "2000",
				},
				marketing_features: [],
				object: "product" as const,
				active: true,
				created: **********,
				description: "Test product",
				images: [],
				livemode: false,
				name: "Test Product",
				package_dimensions: null,
				shippable: true,
				statement_descriptor: null,
				tax_code: null,
				type: "good" as const,
				unit_label: null,
				updated: **********,
				url: null,
			};

			const { unpackPromise } = await import("@/lib/utils");
			vi.mocked(unpackPromise).mockResolvedValue([null, mockEvent]);

			const { cartMetadataSchema } = await import("commerce-kit/internal");
			vi.mocked(cartMetadataSchema.parse).mockReturnValue(mockMetadata);

			const { getProductsFromMetadata } = await import("commerce-kit");
			vi.mocked(getProductsFromMetadata).mockResolvedValue([{ product: mockProduct, quantity: 1 }]);

			const mockStripe: MockStripeProvider = {
				webhooks: {
					constructEventAsync: vi.fn(),
				},
				tax: {
					transactions: {
						createFromCalculation: vi.fn(),
					},
				},
				products: {
					update: vi.fn(),
				},
			};

			const { provider } = await import("commerce-kit");
			vi.mocked(provider).mockReturnValue(mockStripe as unknown as Stripe);

			const request = new Request("http://localhost:3000/api/stripe-webhook", {
				method: "POST",
				body: JSON.stringify(mockEvent),
				headers: {
					"Stripe-Signature": "valid-signature",
				},
			});

			const response = await POST(request);

			expect(response.status).toBe(200);
			const json = await response.json();
			expect(json).toEqual({ received: true });

			expect(mockStripe.tax!.transactions.createFromCalculation).toHaveBeenCalledWith({
				calculation: "taxcalc_123",
				reference: "123", // pi_123 with "pi_" removed
			});

			expect(mockStripe.products!.update).toHaveBeenCalledWith("prod_123", {
				metadata: {
					stock: 4, // Decremented from 5
				},
			});
		});

		it("should handle unhandled event types", async () => {
			const mockEvent = {
				type: "unknown.event",
				data: {
					object: {},
				},
			};

			const { unpackPromise } = await import("@/lib/utils");
			vi.mocked(unpackPromise).mockResolvedValue([null, mockEvent]);

			const mockStripe: MockStripeProvider = {
				webhooks: {
					constructEventAsync: vi.fn(),
				},
			};

			const { provider } = await import("commerce-kit");
			vi.mocked(provider).mockReturnValue(mockStripe as unknown as Stripe);

			const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {});

			const request = new Request("http://localhost:3000/api/stripe-webhook", {
				method: "POST",
				body: JSON.stringify(mockEvent),
				headers: {
					"Stripe-Signature": "valid-signature",
				},
			});

			const response = await POST(request);

			expect(response.status).toBe(200);
			const json = await response.json();
			expect(json).toEqual({ received: true });
			expect(consoleSpy).toHaveBeenCalledWith("Unhandled event type: unknown.event");

			consoleSpy.mockRestore();
		});

		it("should skip product update when stock is infinite", async () => {
			const mockMetadata = {};

			const mockEvent = {
				type: "payment_intent.succeeded",
				data: {
					object: {
						id: "pi_123",
						metadata: mockMetadata,
					},
				},
			};

			const mockProduct = {
				id: "prod_123",
				metadata: {
					slug: "test-product",
					stock: Infinity, // Should skip update
				},
				default_price: {
					id: "price_123",
					object: "price" as const,
					active: true,
					billing_scheme: "per_unit" as const,
					created: **********,
					currency: "usd",
					custom_unit_amount: null,
					livemode: false,
					lookup_key: null,
					metadata: {},
					nickname: null,
					product: "prod_123",
					recurring: null,
					tax_behavior: null,
					tiers_mode: null,
					transform_quantity: null,
					type: "one_time" as const,
					unit_amount: 2000,
					unit_amount_decimal: "2000",
				},
				marketing_features: [],
				object: "product" as const,
				active: true,
				created: **********,
				description: "Test product",
				images: [],
				livemode: false,
				name: "Test Product",
				package_dimensions: null,
				shippable: true,
				statement_descriptor: null,
				tax_code: null,
				type: "good" as const,
				unit_label: null,
				updated: **********,
				url: null,
			};

			const { unpackPromise } = await import("@/lib/utils");
			vi.mocked(unpackPromise).mockResolvedValue([null, mockEvent]);

			const { cartMetadataSchema } = await import("commerce-kit/internal");
			vi.mocked(cartMetadataSchema.parse).mockReturnValue(mockMetadata);

			const { getProductsFromMetadata } = await import("commerce-kit");
			vi.mocked(getProductsFromMetadata).mockResolvedValue([{ product: mockProduct, quantity: 1 }]);

			const mockStripe: MockStripeProvider = {
				webhooks: {
					constructEventAsync: vi.fn(),
				},
				products: {
					update: vi.fn(),
				},
			};

			const { provider } = await import("commerce-kit");
			vi.mocked(provider).mockReturnValue(mockStripe as unknown as Stripe);

			const request = new Request("http://localhost:3000/api/stripe-webhook", {
				method: "POST",
				body: JSON.stringify(mockEvent),
				headers: {
					"Stripe-Signature": "valid-signature",
				},
			});

			const response = await POST(request);

			expect(response.status).toBe(200);
			expect(mockStripe.products!.update).not.toHaveBeenCalled();
		});
	});
});
