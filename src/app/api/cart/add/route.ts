import { type NextRequest, NextResponse } from "next/server";
import { addToCartAction } from "@/actions/cart-actions";
import {
	checkRateLimit,
	createSecurityHeaders,
	getClientIP,
	sanitizeInput,
	validateContentType,
	validateOrigin,
} from "@/lib/security";

export async function POST(request: NextRequest) {
	try {
		const ip = getClientIP(request);

		if (!checkRateLimit(ip)) {
			return NextResponse.json(
				{ error: "Too many requests. Please try again later." },
				{
					status: 429,
					headers: {
						...createSecurityHeaders(),
						"Retry-After": "60",
					},
				},
			);
		}

		if (!validateOrigin(request)) {
			return NextResponse.json(
				{ error: "Invalid request origin" },
				{
					status: 403,
					headers: createSecurityHeaders(),
				},
			);
		}

		if (!validateContentType(request, "multipart/form-data")) {
			return NextResponse.json(
				{ error: "Invalid content type" },
				{
					status: 400,
					headers: createSecurityHeaders(),
				},
			);
		}

		const formData = await request.formData();
		const rawProductId = formData.get("productId");
		const productId = sanitizeInput(rawProductId);

		if (!productId) {
			return NextResponse.json(
				{ error: "Product ID is required" },
				{
					status: 400,
					headers: createSecurityHeaders(),
				},
			);
		}

		const sanitizedFormData = new FormData();
		sanitizedFormData.set("productId", productId);

		const quantity = formData.get("quantity");
		if (quantity) {
			const sanitizedQuantity = sanitizeInput(quantity);
			if (sanitizedQuantity && /^\d+$/.test(sanitizedQuantity)) {
				sanitizedFormData.set("quantity", sanitizedQuantity);
			}
		}

		const cart = await addToCartAction(sanitizedFormData);

		if (cart) {
			return NextResponse.json(
				{
					success: true,
					cartId: cart.id,
					message: "Product added to cart successfully",
				},
				{
					status: 200,
					headers: createSecurityHeaders(),
				},
			);
		} else {
			return NextResponse.json(
				{ error: "Failed to add product to cart" },
				{
					status: 500,
					headers: createSecurityHeaders(),
				},
			);
		}
	} catch (error) {
		console.error("Add to cart API error:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{
				status: 500,
				headers: createSecurityHeaders(),
			},
		);
	}
}
