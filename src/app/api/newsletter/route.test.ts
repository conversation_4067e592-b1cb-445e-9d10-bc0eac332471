import { NextRequest } from "next/server";
import { beforeEach, describe, expect, it } from "vitest";
import { GET, POST } from "./route";

// Import the route to initialize global function
import "./route";

// Type for global test utilities
interface GlobalWithTestUtils {
	__newsletterClearRateLimit?: () => void;
}

// Access the test utility from global
const clearRateLimit = () => {
	const global = globalThis as GlobalWithTestUtils;
	if (typeof globalThis !== "undefined" && global.__newsletterClearRateLimit) {
		global.__newsletterClearRateLimit();
	}
};

interface NewsletterResponse {
	status: number;
	message: string;
	id?: string;
}

describe("/api/newsletter", () => {
	// Reset rate limiting between tests
	beforeEach(() => {
		if (clearRateLimit) {
			clearRateLimit();
		}
	});

	describe("POST", () => {
		it("should reject invalid email address", async () => {
			const request = new NextRequest("http://localhost:3000/api/newsletter", {
				method: "POST",
				body: JSON.stringify({ email: "invalid-email" }),
				headers: {
					"Content-Type": "application/json",
					"x-forwarded-for": "********0",
				},
			});

			const response = await POST(request);
			const data = (await response.json()) as NewsletterResponse;

			expect(response.status).toBe(400);
			expect(data.status).toBe(400);
			expect(data.message).toBe("Invalid email address");
		});

		it("should reject missing email", async () => {
			const request = new NextRequest("http://localhost:3000/api/newsletter", {
				method: "POST",
				body: JSON.stringify({}),
				headers: {
					"Content-Type": "application/json",
					"x-forwarded-for": "********1",
				},
			});

			const response = await POST(request);
			const data = (await response.json()) as NewsletterResponse;

			expect(response.status).toBe(400);
			expect(data.status).toBe(400);
			expect(data.message).toBe("Invalid email address");
		});

		it("should reject empty email", async () => {
			const request = new NextRequest("http://localhost:3000/api/newsletter", {
				method: "POST",
				body: JSON.stringify({ email: "" }),
				headers: {
					"Content-Type": "application/json",
					"x-forwarded-for": "********2",
				},
			});

			const response = await POST(request);
			const data = (await response.json()) as NewsletterResponse;

			expect(response.status).toBe(400);
			expect(data.status).toBe(400);
			expect(data.message).toBe("Invalid email address");
		});

		it("should reject non-string email", async () => {
			const request = new NextRequest("http://localhost:3000/api/newsletter", {
				method: "POST",
				body: JSON.stringify({ email: 123 }),
				headers: {
					"Content-Type": "application/json",
					"x-forwarded-for": "********3",
				},
			});

			const response = await POST(request);
			const data = (await response.json()) as NewsletterResponse;

			expect(response.status).toBe(400);
			expect(data.status).toBe(400);
			expect(data.message).toBe("Invalid email address");
		});

		it("should reject email without domain", async () => {
			const request = new NextRequest("http://localhost:3000/api/newsletter", {
				method: "POST",
				body: JSON.stringify({ email: "user@" }),
				headers: {
					"Content-Type": "application/json",
					"x-forwarded-for": "********4",
				},
			});

			const response = await POST(request);
			const data = (await response.json()) as NewsletterResponse;

			expect(response.status).toBe(400);
			expect(data.status).toBe(400);
			expect(data.message).toBe("Invalid email address");
		});

		it("should reject email without TLD", async () => {
			const request = new NextRequest("http://localhost:3000/api/newsletter", {
				method: "POST",
				body: JSON.stringify({ email: "user@domain" }),
				headers: {
					"Content-Type": "application/json",
					"x-forwarded-for": "********5",
				},
			});

			const response = await POST(request);
			const data = (await response.json()) as NewsletterResponse;

			expect(response.status).toBe(400);
			expect(data.status).toBe(400);
			expect(data.message).toBe("Invalid email address");
		});

		it("should reject email with spaces", async () => {
			const request = new NextRequest("http://localhost:3000/api/newsletter", {
				method: "POST",
				body: JSON.stringify({ email: "user @domain.com" }),
				headers: {
					"Content-Type": "application/json",
					"x-forwarded-for": "********6",
				},
			});

			const response = await POST(request);
			const data = (await response.json()) as NewsletterResponse;

			expect(response.status).toBe(400);
			expect(data.status).toBe(400);
			expect(data.message).toBe("Invalid email address");
		});

		it("should reject extremely long email", async () => {
			const longEmail = "a".repeat(250) + "@domain.com";
			const request = new NextRequest("http://localhost:3000/api/newsletter", {
				method: "POST",
				body: JSON.stringify({ email: longEmail }),
				headers: {
					"Content-Type": "application/json",
					"x-forwarded-for": "*********",
				},
			});

			const response = await POST(request);
			const data = (await response.json()) as NewsletterResponse;

			expect(response.status).toBe(400);
			expect(data.status).toBe(400);
			expect(data.message).toBe("Invalid email address");
		});

		it("should enforce rate limiting", async () => {
			// Clear rate limit to ensure clean state
			if (clearRateLimit) {
				clearRateLimit();
			}

			const createRequest = (i: number) =>
				new NextRequest("http://localhost:3000/api/newsletter", {
					method: "POST",
					body: JSON.stringify({ email: `test${i}@valid-domain.co.uk` }),
					headers: {
						"Content-Type": "application/json",
						"x-forwarded-for": "********",
					},
				});

			// First 3 requests should not be rate limited (but will fail due to missing API key)
			for (let i = 0; i < 3; i++) {
				const request = createRequest(i);
				const response = await POST(request);
				// Should not be rate limited (status 500 for missing API key is expected)
				expect(response.status).not.toBe(429);
			}

			// 4th and 5th requests should be rate limited
			for (let i = 3; i < 5; i++) {
				const request = createRequest(i);
				const response = await POST(request);
				const data = (await response.json()) as NewsletterResponse;

				expect(response.status).toBe(429);
				expect(data.status).toBe(429);
				expect(data.message).toBe("Too many requests. Please try again later.");
			}
		});
	});

	describe("GET", () => {
		it("should return endpoint status", async () => {
			const response = await GET();
			const data = (await response.json()) as NewsletterResponse;

			expect(response.status).toBe(200);
			expect(data.message).toBe("Newsletter API endpoint");
		});
	});
});
