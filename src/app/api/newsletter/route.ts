import { type NextRequest, NextResponse } from "next/server";
import { Resend } from "resend";
import { env } from "@/env.mjs";

// Simple in-memory rate limiting
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 3; // 3 requests per minute per IP

function getResend() {
	return new Resend(env.RESEND_API_KEY);
}

function checkRateLimit(ip: string): boolean {
	const now = Date.now();
	const record = rateLimitMap.get(ip);

	if (!record || now > record.resetTime) {
		rateLimitMap.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
		return true;
	}

	if (record.count >= RATE_LIMIT_MAX_REQUESTS) {
		return false;
	}

	record.count++;
	return true;
}

function getClientIP(request: NextRequest): string {
	const forwarded = request.headers.get("x-forwarded-for");
	const realIP = request.headers.get("x-real-ip");
	const ip = forwarded?.split(",")[0] || realIP || "unknown";
	return ip.trim();
}

function isValidEmail(email: string): boolean {
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	return emailRegex.test(email) && email.length <= 254;
}

// Internal function for testing - not exported to avoid Next.js route type conflicts
function clearRateLimitInternal(): void {
	rateLimitMap.clear();
}

// Global access for testing only - used by test-utils module
interface GlobalWithTestUtils {
	__newsletterClearRateLimit?: () => void;
}

if (typeof globalThis !== "undefined") {
	(globalThis as GlobalWithTestUtils).__newsletterClearRateLimit = clearRateLimitInternal;
}

export async function POST(request: NextRequest) {
	try {
		// Rate limiting check
		const clientIP = getClientIP(request);
		if (!checkRateLimit(clientIP)) {
			return NextResponse.json(
				{ status: 429, message: "Too many requests. Please try again later." },
				{ status: 429 },
			);
		}

		const body = (await request.json()) as { email?: unknown };
		const { email } = body;

		if (!email || typeof email !== "string" || !isValidEmail(email)) {
			return NextResponse.json({ status: 400, message: "Invalid email address" }, { status: 400 });
		}

		const fromEmail = env.NEWSLETTER_FROM_EMAIL || "<EMAIL>";
		const fromName = env.NEWSLETTER_FROM_NAME || "Your Store";

		const resend = getResend();
		const { data, error } = await resend.emails.send({
			from: `${fromName} <${fromEmail}>`,
			to: email,
			subject: "Welcome to our newsletter!",
			html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Welcome to ${fromName}!</h2>
          <p>Thank you for subscribing to our newsletter.
          <hr style="margin: 20px 0; border: none; border-top: 1px solid #eee;">
          <p style="font-size: 12px; color: #666;">
            You can unsubscribe at any time by replying to any of our emails.
          </p>
        </div>
      `,
		});

		if (error) {
			console.error("Resend error:", error);
			return NextResponse.json({ status: 500, message: "Failed to send email" }, { status: 500 });
		}

		console.log("Newsletter signup successful:", { email, messageId: data?.id });

		return NextResponse.json({
			status: 200,
			message: "Successfully subscribed to newsletter",
			id: data?.id,
		});
	} catch (error) {
		console.error("Newsletter API error:", error);
		return NextResponse.json({ status: 500, message: "Internal server error" }, { status: 500 });
	}
}

export async function GET() {
	return NextResponse.json({ message: "Newsletter API endpoint" }, { status: 200 });
}
