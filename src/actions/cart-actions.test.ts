import * as Commerce from "commerce-kit";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { clearCartCookie, getCartCookie<PERSON>son, setCartCookie<PERSON>son } from "@/lib/cart";

// Mock dependencies completely to avoid type complexity
vi.mock("@/lib/cart");
vi.mock("commerce-kit", () => ({
	cartGet: vi.fn(),
	cartCreate: vi.fn(),
	cartAdd: vi.fn(),
	cartChangeQuantity: vi.fn(),
	cartSetQuantity: vi.fn(),
	cartCount: vi.fn(),
}));
vi.mock("next/cache", () => ({
	revalidateTag: vi.fn(),
}));

const mockGetCartCookieJson = vi.mocked(getCartCookieJson);
const mockSetCartCookieJson = vi.mocked(setCartCookieJson);
const mockClearCartCookie = vi.mocked(clearCartCookie);

// Use simpler mock functions that return the expected shapes
const mockCommerceCartGet = vi.fn();
const mockCommerceCartCreate = vi.fn();
const mockCommerceCartAdd = vi.fn();
const mockCommerceCartChangeQuantity = vi.fn();
const mockCommerceCartSetQuantity = vi.fn();
const mockCommerceCartCount = vi.fn();

// Override the module mocks with our specific mock functions
vi.mocked(Commerce.cartGet).mockImplementation(mockCommerceCartGet);
vi.mocked(Commerce.cartCreate).mockImplementation(mockCommerceCartCreate);
vi.mocked(Commerce.cartAdd).mockImplementation(mockCommerceCartAdd);
vi.mocked(Commerce.cartChangeQuantity).mockImplementation(mockCommerceCartChangeQuantity);
vi.mocked(Commerce.cartSetQuantity).mockImplementation(mockCommerceCartSetQuantity);
vi.mocked(Commerce.cartCount).mockImplementation(mockCommerceCartCount);

// Import actions after mocking
import {
	addToCartAction,
	clearCartCookieAction,
	decreaseQuantity,
	findOrCreateCartIdFromCookiesAction,
	getCartFromCookiesAction,
	increaseQuantity,
	setInitialCartCookiesAction,
	setQuantity,
} from "./cart-actions";

describe("Cart Actions", () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	describe("getCartFromCookiesAction", () => {
		it("should return null when no cart cookie exists", async () => {
			mockGetCartCookieJson.mockResolvedValue(null);

			const result = await getCartFromCookiesAction();

			expect(result).toBeNull();
			expect(mockGetCartCookieJson).toHaveBeenCalledOnce();
		});

		it("should return cart when cookie exists and cart is found", async () => {
			const mockCartCookie = { id: "cart-123", linesCount: 2 };
			const mockCart = { cart: { id: "cart-123" }, lines: [], shippingRate: null };

			mockGetCartCookieJson.mockResolvedValue(mockCartCookie);
			mockCommerceCartGet.mockResolvedValue(mockCart);

			const result = await getCartFromCookiesAction();

			expect(result).toEqual(mockCart);
			expect(mockCommerceCartGet).toHaveBeenCalledWith("cart-123");
		});

		it("should return null when cart cookie exists but cart is not found", async () => {
			const mockCartCookie = { id: "cart-123", linesCount: 2 };

			mockGetCartCookieJson.mockResolvedValue(mockCartCookie);
			mockCommerceCartGet.mockResolvedValue(null);

			const result = await getCartFromCookiesAction();

			expect(result).toBeNull();
		});
	});

	describe("setInitialCartCookiesAction", () => {
		it("should set cart cookie with provided data", async () => {
			await setInitialCartCookiesAction("cart-456", 3);

			expect(mockSetCartCookieJson).toHaveBeenCalledWith({
				id: "cart-456",
				linesCount: 3,
			});
		});
	});

	describe("findOrCreateCartIdFromCookiesAction", () => {
		it("should return existing cart when found", async () => {
			const mockCart = { cart: { id: "cart-123" }, lines: [], shippingRate: null };

			mockGetCartCookieJson.mockResolvedValue({ id: "cart-123", linesCount: 2 });
			mockCommerceCartGet.mockResolvedValue(mockCart);

			const result = await findOrCreateCartIdFromCookiesAction();

			expect(result).toEqual(mockCart);
			expect(mockCommerceCartCreate).not.toHaveBeenCalled();
		});

		it("should create new cart when none exists", async () => {
			const mockNewCart = { id: "cart-new" };

			mockGetCartCookieJson.mockResolvedValue(null);
			mockCommerceCartCreate.mockResolvedValue(mockNewCart);

			const result = await findOrCreateCartIdFromCookiesAction();

			expect(result).toBe("cart-new");
			expect(mockCommerceCartCreate).toHaveBeenCalledOnce();
			expect(mockSetCartCookieJson).toHaveBeenCalledWith({
				id: "cart-new",
				linesCount: 0,
			});
		});
	});

	describe("clearCartCookieAction", () => {
		it("should clear cart cookie when one exists", async () => {
			const mockCartCookie = { id: "cart-123", linesCount: 1 };

			mockGetCartCookieJson.mockResolvedValue(mockCartCookie);

			await clearCartCookieAction();

			expect(mockClearCartCookie).toHaveBeenCalledOnce();
		});

		it("should do nothing when no cart cookie exists", async () => {
			mockGetCartCookieJson.mockResolvedValue(null);

			await clearCartCookieAction();

			expect(mockClearCartCookie).not.toHaveBeenCalled();
		});
	});

	describe("addToCartAction", () => {
		it("should add product to cart successfully", async () => {
			const formData = new FormData();
			formData.append("productId", "prod-123");

			const mockCart = { cart: { id: "cart-123" }, lines: [], shippingRate: null };
			const mockUpdatedCart = { id: "cart-123", metadata: { lines: 2 } };

			mockGetCartCookieJson.mockResolvedValue({ id: "cart-123", linesCount: 1 });
			mockCommerceCartGet.mockResolvedValue(mockCart);
			mockCommerceCartAdd.mockResolvedValue(mockUpdatedCart);
			mockCommerceCartCount.mockReturnValue(2);

			const result = await addToCartAction(formData);

			expect(mockCommerceCartAdd).toHaveBeenCalledWith({
				productId: "prod-123",
				cartId: "cart-123",
			});
			expect(mockSetCartCookieJson).toHaveBeenCalledWith({
				id: "cart-123",
				linesCount: 2,
			});
			expect(result).toEqual(mockUpdatedCart);
		});

		it("should throw error for invalid product ID", async () => {
			const formData = new FormData();
			formData.append("productId", "");

			await expect(addToCartAction(formData)).rejects.toThrow("Invalid product ID");
		});

		it("should throw error for missing product ID", async () => {
			const formData = new FormData();

			await expect(addToCartAction(formData)).rejects.toThrow("Invalid product ID");
		});
	});

	describe("increaseQuantity", () => {
		it("should increase quantity successfully", async () => {
			const mockCart = { cart: { id: "cart-123" }, lines: [], shippingRate: null };
			const mockUpdatedCart = { id: "cart-123", metadata: { lines: 3 } };

			mockGetCartCookieJson.mockResolvedValue({ id: "cart-123", linesCount: 2 });
			mockCommerceCartGet.mockResolvedValue(mockCart);
			mockCommerceCartChangeQuantity.mockResolvedValue(mockUpdatedCart);
			mockCommerceCartCount.mockReturnValue(3);

			const result = await increaseQuantity("prod-123");

			expect(mockCommerceCartChangeQuantity).toHaveBeenCalledWith({
				productId: "prod-123",
				cartId: "cart-123",
				operation: "INCREASE",
			});
			expect(mockSetCartCookieJson).toHaveBeenCalledWith({
				id: "cart-123",
				linesCount: 3,
			});
			expect(result).toEqual(mockUpdatedCart);
		});

		it("should throw error when cart not found", async () => {
			mockGetCartCookieJson.mockResolvedValue(null);

			await expect(increaseQuantity("prod-123")).rejects.toThrow("Cart not found");
		});
	});

	describe("decreaseQuantity", () => {
		it("should decrease quantity successfully", async () => {
			const mockCart = { cart: { id: "cart-123" }, lines: [], shippingRate: null };
			const mockUpdatedCart = { id: "cart-123", metadata: { lines: 1 } };

			mockGetCartCookieJson.mockResolvedValue({ id: "cart-123", linesCount: 2 });
			mockCommerceCartGet.mockResolvedValue(mockCart);
			mockCommerceCartChangeQuantity.mockResolvedValue(mockUpdatedCart);
			mockCommerceCartCount.mockReturnValue(1);

			const result = await decreaseQuantity("prod-123");

			expect(mockCommerceCartChangeQuantity).toHaveBeenCalledWith({
				productId: "prod-123",
				cartId: "cart-123",
				operation: "DECREASE",
			});
			expect(mockSetCartCookieJson).toHaveBeenCalledWith({
				id: "cart-123",
				linesCount: 1,
			});
			expect(result).toEqual(mockUpdatedCart);
		});

		it("should throw error when cart not found", async () => {
			mockGetCartCookieJson.mockResolvedValue(null);

			await expect(decreaseQuantity("prod-123")).rejects.toThrow("Cart not found");
		});
	});

	describe("setQuantity", () => {
		it("should set quantity successfully", async () => {
			const mockCart = { cart: { id: "cart-123" }, lines: [], shippingRate: null };

			mockGetCartCookieJson.mockResolvedValue({ id: "cart-123", linesCount: 2 });
			mockCommerceCartGet.mockResolvedValue(mockCart);

			await setQuantity({
				productId: "prod-123",
				cartId: "cart-123",
				quantity: 5,
			});

			expect(mockCommerceCartSetQuantity).toHaveBeenCalledWith({
				productId: "prod-123",
				cartId: "cart-123",
				quantity: 5,
			});
		});

		it("should throw error when cart not found", async () => {
			mockGetCartCookieJson.mockResolvedValue(null);

			await expect(
				setQuantity({
					productId: "prod-123",
					cartId: "cart-123",
					quantity: 5,
				}),
			).rejects.toThrow("Cart not found");
		});
	});
});
