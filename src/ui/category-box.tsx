import Image from "next/image";
import { getTranslations } from "@/i18n/server";
import type { Category } from "@/store.config";
import { YnsLink } from "@/ui/yns-link";

export async function CategoryBox({ category }: { category: Category }) {
	const t = await getTranslations("Global.actions");

	return (
		<YnsLink href={`/category/${category.slug}`} className="group relative">
			<div className="relative overflow-hidden rounded-lg">
				<Image
					alt={`${category.name} category`}
					className="w-full scale-105 object-cover transition-all group-hover:scale-100 group-hover:opacity-75"
					sizes="(max-width: 1024x) 100vw, (max-width: 1280px) 50vw, 620px"
					src={category.image}
				/>
			</div>
			<div className="justify-end gap-2 px-4 py-2 text-neutral-600">
				<h3 className="text-lg font-bold tracking-tight">{category.name}</h3>
				{category.description && <p className="text-sm text-neutral-500">{category.description}</p>}
				<p className="mt-1 font-medium">{t("shopNow")}</p>
			</div>
		</YnsLink>
	);
}
