"use client";

import type { NormalSearchFilters } from "@/lib/search/enhanced-normal-search";
import { Button } from "@/ui/shadcn/button";
import { Input } from "@/ui/shadcn/input";
import { Label } from "@/ui/shadcn/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/ui/shadcn/select";

interface SearchFiltersProps {
	filters: NormalSearchFilters;
	categories: string[];
	priceRange: { min: number; max: number };
	onFiltersChange: (filters: NormalSearchFilters) => void;
	onClearFilters: () => void;
}

export function SearchFilters({
	filters,
	categories,
	priceRange,
	onFiltersChange,
	onClearFilters,
}: SearchFiltersProps) {
	const handleCategoryChange = (category: string) => {
		onFiltersChange({
			...filters,
			category: category === "all" ? undefined : category,
		});
	};

	const handleSortChange = (sortBy: string) => {
		onFiltersChange({
			...filters,
			sortBy: sortBy as NormalSearchFilters["sortBy"],
		});
	};

	const handlePriceRangeChange = (min: number, max: number) => {
		onFiltersChange({
			...filters,
			priceRange: { min, max },
		});
	};

	const hasActiveFilters =
		filters.category || filters.priceRange || (filters.sortBy && filters.sortBy !== "relevance");

	return (
		<div className="space-y-4 rounded-lg border bg-background p-4" role="search" aria-label="Product filters">
			<div className="flex items-center justify-between">
				<h3 className="text-lg font-semibold">Filters</h3>
				{hasActiveFilters && (
					<Button variant="outline" size="sm" onClick={onClearFilters} aria-label="Clear all filters">
						Clear Filters
					</Button>
				)}
			</div>

			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				{/* Category Filter */}
				<div className="space-y-2">
					<Label htmlFor="category-filter">Category</Label>
					<Select value={filters.category || "all"} onValueChange={handleCategoryChange}>
						<SelectTrigger id="category-filter">
							<SelectValue placeholder="All Categories" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All Categories</SelectItem>
							{categories.map((category) => (
								<SelectItem key={category} value={category}>
									{category}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				{/* Sort Filter */}
				<div className="space-y-2">
					<Label htmlFor="sort-filter">Sort By</Label>
					<Select value={filters.sortBy || "relevance"} onValueChange={handleSortChange}>
						<SelectTrigger id="sort-filter">
							<SelectValue placeholder="Relevance" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="relevance">Relevance</SelectItem>
							<SelectItem value="price-asc">Price: Low to High</SelectItem>
							<SelectItem value="price-desc">Price: High to Low</SelectItem>
							<SelectItem value="name">Name A-Z</SelectItem>
							<SelectItem value="newest">Newest</SelectItem>
						</SelectContent>
					</Select>
				</div>

				{/* Price Range Filters */}
				<div className="space-y-2">
					<Label htmlFor="min-price">Min Price</Label>
					<Input
						id="min-price"
						type="number"
						min={priceRange.min}
						max={priceRange.max}
						value={filters.priceRange?.min || ""}
						onChange={(e) => {
							const min = parseFloat(e.target.value) || priceRange.min;
							const max = filters.priceRange?.max || priceRange.max;
							handlePriceRangeChange(min, max);
						}}
						placeholder={`Min (${priceRange.min})`}
					/>
				</div>

				<div className="space-y-2">
					<Label htmlFor="max-price">Max Price</Label>
					<Input
						id="max-price"
						type="number"
						min={priceRange.min}
						max={priceRange.max}
						value={filters.priceRange?.max || ""}
						onChange={(e) => {
							const max = parseFloat(e.target.value) || priceRange.max;
							const min = filters.priceRange?.min || priceRange.min;
							handlePriceRangeChange(min, max);
						}}
						placeholder={`Max (${priceRange.max})`}
					/>
				</div>
			</div>

			{/* Filter Summary */}
			{hasActiveFilters && (
				<div className="flex flex-wrap gap-2 pt-2">
					{filters.category && (
						<div className="flex items-center gap-1 rounded-full bg-primary/10 px-3 py-1 text-sm">
							<span>Category: {filters.category}</span>
							<Button
								variant="ghost"
								size="sm"
								className="h-auto p-0"
								onClick={() => handleCategoryChange("all")}
								aria-label={`Remove category filter: ${filters.category}`}
							>
								×
							</Button>
						</div>
					)}
					{filters.priceRange && (
						<div className="flex items-center gap-1 rounded-full bg-primary/10 px-3 py-1 text-sm">
							<span>
								Price: ${filters.priceRange.min} - ${filters.priceRange.max}
							</span>
							<Button
								variant="ghost"
								size="sm"
								className="h-auto p-0"
								onClick={() => onFiltersChange({ ...filters, priceRange: undefined })}
								aria-label="Remove price range filter"
							>
								×
							</Button>
						</div>
					)}
					{filters.sortBy && filters.sortBy !== "relevance" && (
						<div className="flex items-center gap-1 rounded-full bg-primary/10 px-3 py-1 text-sm">
							<span>Sort: {filters.sortBy}</span>
							<Button
								variant="ghost"
								size="sm"
								className="h-auto p-0"
								onClick={() => handleSortChange("relevance")}
								aria-label={`Remove sort filter: ${filters.sortBy}`}
							>
								×
							</Button>
						</div>
					)}
				</div>
			)}
		</div>
	);
}
