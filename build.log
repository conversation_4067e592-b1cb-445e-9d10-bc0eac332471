$ next build
 ⚠ Warning: Found multiple lockfiles. Selecting /Users/<USER>/package-lock.json.
   Consider removing the lockfiles at:
   * /Users/<USER>/Documents/development/yournextstore/bun.lock

   ▲ Next.js 15.4.0-canary.119
   - Environments: .env
   - Experiments (use with caution):
     ✓ ppr
     · cpus: 1
     ✓ mdxRs
     ✓ inlineCss
     ✓ reactCompiler
     ✓ scrollRestoration

   Creating an optimized production build ...
 ✓ Compiled successfully in 2000ms
   Skipping linting
   Checking validity of types ...
   Collecting page data ...
❌ Invalid environment variables: [
  {
    code: 'custom',
    message: 'PASSWORD must be a bcrypt hash in production (should start with $2a$, $2b$, $2x$, or $2y$)',
    path: [ 'PASSWORD' ]
  }
]
Error: Invalid environment variables
    at <unknown> (.next/server/app/api/newsletter/route.js:10:9385)
    at <unknown> (.next/server/app/api/newsletter/route.js:10:9557)
    at e (.next/server/app/api/newsletter/route.js:10:9811)
    at 64115 (.next/server/app/api/newsletter/route.js:10:6118)
    at c (.next/server/webpack-runtime.js:1:128)
    at 47978 (.next/server/app/api/newsletter/route.js:1:11165)
    at c (.next/server/webpack-runtime.js:1:128)
    at <unknown> (.next/server/app/api/newsletter/route.js:10:10970)
    at c.X (.next/server/webpack-runtime.js:1:2403)
    at <unknown> (.next/server/app/api/newsletter/route.js:10:10946)

> Build error occurred
[Error: Failed to collect page data for /api/newsletter] {
  type: 'Error'
}
error: script "build" exited with code 1
