{"$schema": "https://biomejs.dev/schemas/latest/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "useEditorconfig": true, "formatWithErrors": false, "indentStyle": "tab", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 110, "attributePosition": "auto", "bracketSpacing": true}, "linter": {"enabled": true, "rules": {"recommended": false, "complexity": {"noBannedTypes": "error", "noUselessThisAlias": "error", "noUselessTypeConstraint": "error", "useLiteralKeys": "error", "useOptionalChain": "error"}, "correctness": {"noEmptyPattern": "off", "noPrecisionLoss": "error", "noUnusedVariables": "off"}, "style": {"noDefaultExport": "off", "noInferrableTypes": "error", "noNamespace": "error", "useAsConstAssertion": "error", "useBlockStatements": "off", "useConsistentArrayType": "off", "useForOf": "error", "useImportType": "error", "useShorthandFunctionType": "error"}, "suspicious": {"noEmptyBlockStatements": "off", "noEmptyInterface": "off", "noExplicitAny": "error", "noExtraNonNullAssertion": "error", "noMisleadingInstantiator": "error", "noUnsafeDeclarationMerging": "error", "useAwait": "off", "useNamespaceKeyword": "error"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto", "bracketSpacing": true}}, "overrides": [{"includes": ["**/src/i18n.ts", "**/*.d.ts", "**/tailwind.config.ts", "**/prettier.config.js", "**/middleware.ts", "**/commitlint.config.ts", "**/vitest.config.ts"], "linter": {"rules": {"style": {"noDefaultExport": "off"}}}}]}