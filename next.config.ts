import MDX from "@next/mdx";
import type { NextConfig } from "next/types";

const withMDX = MDX();

const nextConfig: NextConfig = {
	reactStrictMode: true,
	eslint: {
		ignoreDuringBuilds: true,
	},
	output: process.env.DOCKER ? "standalone" : undefined,
	headers: async () => [
		{
			source: "/(.*)",
			headers: [
				{
					key: "X-Frame-Options",
					value: "DENY",
				},
				{
					key: "X-Content-Type-Options",
					value: "nosniff",
				},
				{
					key: "Referrer-Policy",
					value: "strict-origin-when-cross-origin",
				},
				{
					key: "Permissions-Policy",
					value: "camera=(), microphone=(), geolocation=()",
				},
				...(process.env.NODE_ENV === "production"
					? [
							{
								key: "Strict-Transport-Security",
								value: "max-age=63072000; includeSubDomains; preload",
							},
							{
								key: "Content-Security-Policy",
								value: [
									"default-src 'self'",
									"script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://eu.umami.is",
									"style-src 'self' 'unsafe-inline'",
									"img-src 'self' data: https://files.stripe.com https://d1wqzb5bdbcre6.cloudfront.net https://*.blob.vercel-storage.com",
									"font-src 'self' data:",
									"connect-src 'self' https://api.stripe.com https://eu.umami.is",
									"frame-src https://js.stripe.com",
									"object-src 'none'",
									"base-uri 'self'",
									"form-action 'self'",
									"frame-ancestors 'none'",
									"upgrade-insecure-requests",
								].join("; "),
							},
						]
					: [
							{
								key: "Content-Security-Policy",
								value: [
									"default-src 'self'",
									"script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com",
									"style-src 'self' 'unsafe-inline'",
									"img-src 'self' data: https://files.stripe.com https://d1wqzb5bdbcre6.cloudfront.net https://*.blob.vercel-storage.com",
									"font-src 'self' data:",
									"connect-src 'self' https://api.stripe.com",
									"frame-src https://js.stripe.com",
									"object-src 'none'",
									"base-uri 'self'",
									"form-action 'self'",
									"frame-ancestors 'none'",
								].join("; "),
							},
						]),
			],
		},
	],
	logging: {
		fetches: {
			fullUrl: true,
		},
	},
	images: {
		remotePatterns: [
			{ hostname: "files.stripe.com" },
			{ hostname: "d1wqzb5bdbcre6.cloudfront.net" },
			{ hostname: "*.blob.vercel-storage.com" },
		],
		formats: ["image/avif", "image/webp"],
	},
	transpilePackages: ["next-mdx-remote", "commerce-kit"],
	experimental: {
		esmExternals: true,
		scrollRestoration: true,
		ppr: true,
		cpus: 1,
		reactCompiler: true,
		mdxRs: true,
		inlineCss: true,
	},
	webpack: (config) => {
		return {
			...config,
			resolve: {
				...config.resolve,
				extensionAlias: {
					".js": [".js", ".ts"],
					".jsx": [".jsx", ".tsx"],
				},
			},
		};
	},
	rewrites: async () => [
		{
			source: "/stats/:match*",
			destination: "https://eu.umami.is/:match*",
		},
	],
};

export default withMDX(nextConfig);
