# Your Next Store - Codebase Analysis

## Project Overview

**Your Next Store (YNS)** is a modern, production-ready e-commerce platform built with Next.js 15, TypeScript, and Stripe integration. It's designed as a complete e-commerce solution with server-side rendering, internationalization, and AI-powered features.

### Key Characteristics

- **Version**: 1.12.0
- **License**: AGPL-3.0-only (with commercial license available)
- **Architecture**: Server-first with selective client components
- **Backend**: Stripe-centric (products, pricing, metadata managed in Stripe)
- **Deployment**: Optimized for Vercel with Docker support

## Technology Stack

### Core Dependencies

- **Next.js**: 15.4.0-canary.119 (App Router, React 19, experimental features)
- **React**: 19.1.0 with React Compiler
- **TypeScript**: 5.8.3 (strict mode)
- **Database**: Stripe (products, pricing, orders)
- **Authentication**: JWT-based sessions with jose library
- **State Management**: React Context + Server Actions

### Key Libraries

- **Stripe Integration**: stripe-js, react-stripe-js, commerce-kit
- **Search**: Fuse.js (fuzzy search), Trieve SDK
- **UI**: Radix UI primitives, Tailwind CSS v4, shadcn/ui
- **Forms**: Zod validation, React Hook Form
- **Internationalization**: next-intl
- **Testing**: Vitest, React Testing Library

## Architecture Analysis

### Directory Structure

```
src/
├── app/                    # App Router pages and API routes
│   ├── (store)/           # Store-facing pages (grouped route)
│   └── api/               # API endpoints (Stripe webhook, chat)
├── actions/               # Server Actions
├── lib/                   # Utilities and business logic
│   ├── search/           # Search implementations
│   ├── auth.ts           # JWT session management
│   └── cart.ts           # Cart cookie utilities
├── ui/                    # UI components
│   ├── shadcn/           # shadcn/ui components
│   ├── checkout/         # Checkout flow components
│   └── nav/              # Navigation components
└── i18n/                 # Internationalization
```

### Key Architectural Patterns

#### 1. Server Components First

- Most components are Server Components by default
- Client components marked with `"use client"` for:
  - Interactive forms and modals
  - Real-time state updates
  - Browser-only features

#### 2. Stripe-Centric Architecture

- **Products**: Defined in Stripe with metadata fields
- **Pricing**: Managed entirely through Stripe
- **Cart**: State managed via secure HTTP-only cookies (`yns_cart`)
- **Webhooks**: Handle payment events at `/api/stripe-webhook`

#### 3. Commerce Kit Integration

- Custom commerce library (`commerce-kit` package)
- Provides type-safe Stripe operations
- Handles product mapping, cart operations, checkout

## Key Features

### 1. Enhanced Search System

**Location**: `src/lib/search/enhanced-normal-search.ts`

#### Architecture

- **Unified Backend**: Single `enhancedNormalSearch` function
- **Hybrid Approach**:
  - Small catalogs (<500 products): In-memory fuzzy search with Fuse.js
  - Large catalogs: Live search with streaming pagination
- **Filters**: Category, price range, sorting
- **Performance**: Next.js `unstable_cache` for search index caching

#### Features

- Fuzzy search with configurable threshold (0.4)
- Real-time filtering and sorting
- URL-synchronized filter states
- SEO-friendly search URLs

### 2. Cart Management

**Location**: `src/actions/cart-actions.ts`

#### Architecture

- **State**: HTTP-only cookies (`yns_cart`)
- **Operations**: Server Actions for all cart operations
- **Validation**: Type-safe with Zod schemas
- **Real-time**: Cart count updates via revalidation tags

#### Operations

- Add to cart with variants
- Quantity management (increase/decrease/set)
- Cart persistence across sessions
- Real-time cart count updates

### 3. Authentication System

**Location**: `src/lib/auth.ts`

#### Architecture

- **JWT-based sessions** with jose library
- **Simple email/password** auth for order management
- **Middleware protection** for admin routes
- **Secure**: bcrypt password hashing, HTTP-only cookies

### 4. Internationalization

**Location**: `src/i18n/`

#### Architecture

- **next-intl** integration
- **Server-side** translation functions
- **Client-side** provider for dynamic content
- **Message files** in `/messages/` directory

### 5. Stripe Webhook Handling

**Location**: `src/app/api/stripe-webhook/route.ts`

#### Events Handled

- **Product changes**: Cache invalidation
- **Price changes**: Cache invalidation
- **Payment success**: Tax calculation, stock management
- **Order updates**: Cart revalidation

## Security Features

### 1. Content Security Policy

- **Production**: Strict CSP with nonce-based script-src
- **Development**: Relaxed CSP for development
- **Headers**: X-Frame-Options, X-Content-Type-Options, HSTS

### 2. Input Validation

- **Zod schemas** for all form inputs
- **Type-safe** server actions
- **SQL injection** prevention via parameterized queries

### 3. Authentication Security

- **bcrypt** password hashing
- **JWT tokens** with expiration
- **HTTP-only cookies** for session management
- **CSRF protection** via same-site cookies

## Performance Optimizations

### 1. Next.js Features

- **Partial Prerendering (PPR)**: Experimental feature enabled
- **React Compiler**: Automatic optimization
- **Image Optimization**: AVIF/WebP formats, remote patterns
- **Bundle Optimization**: transpilePackages, extensionAlias

### 2. Caching Strategy

- **Search metadata**: 1-hour cache with revalidation
- **Product data**: Tagged cache with Stripe webhook invalidation
- **Static assets**: Long-term caching
- **API responses**: Appropriate cache headers

### 3. Database Optimization

- **Stripe CDN**: Image optimization via Stripe's CDN
- **Lazy loading**: Components and data
- **Code splitting**: Automatic via Next.js

## Development Experience

### 1. Type Safety

- **Strict TypeScript**: No implicit any, strict null checks
- **Type-safe** Stripe operations via commerce-kit
- **Zod schemas** for runtime validation
- **Branded types** for domain-specific values

### 2. Testing

- **Vitest**: Fast unit testing
- **React Testing Library**: Component testing
- **Integration tests**: Cart operations, search functionality
- **Type-level tests**: TypeScript compiler validation

### 3. Code Quality

- **Biome**: Linting and formatting (replaces ESLint + Prettier)
- **Husky**: Git hooks for quality gates
- **Commitlint**: Conventional commits
- **Semantic release**: Automated versioning

## Deployment & Environment

### 1. Vercel Deployment

- **One-click deploy**: Button in README
- **Environment variables**: Stripe keys, currency, URL
- **Corepack**: Required for bun support
- **Edge runtime**: Optimized for serverless

### 2. Docker Support

- **Multi-stage build**: Optimized for production
- **Standalone output**: Minimal image size
- **Health checks**: Container health monitoring
- **Environment**: DOCKER=1 flag

### 3. Environment Variables

**Required**:

- `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`
- `STRIPE_SECRET_KEY`
- `STRIPE_CURRENCY`
- `NEXT_PUBLIC_URL`

**Optional**:

- `STRIPE_WEBHOOK_SECRET`
- `ENABLE_STRIPE_TAX`
- `RESEND_API_KEY` (newsletter)
- `NEXT_PUBLIC_UMAMI_WEBSITE_ID`

## Business Logic

### 1. Product Management

- **Stripe as source**: All products managed in Stripe dashboard
- **Metadata mapping**: slug, category, variant, order
- **Variants**: Multiple products with same slug, different variants
- **Categories**: Used for grouping and filtering

### 2. Checkout Flow

- **Stripe Checkout**: Hosted checkout pages
- **Tax calculation**: Optional Stripe Tax integration
- **Stock management**: Automatic stock updates on purchase
- **Order management**: Simple email/password auth for orders

### 3. Search & Discovery

- **Fuzzy search**: Tolerant of typos and variations
- **Filtering**: Category, price range, sorting
- **SEO**: Search URLs are indexable
- **Performance**: Optimized for large catalogs

## Future Roadmap

### Planned Features

- **Admin dashboard**: Product management UI
- **Multi-tenant**: Support for multiple stores
- **Subscriptions**: Recurring payment support
- **Digital products**: Downloadable products
- **Multi-currency**: Support for multiple currencies
- **Advanced analytics**: Sales reporting, customer insights

## Summary

Your Next Store represents a modern, production-ready e-commerce solution that prioritizes:

- **Developer experience**: Type safety, testing, DX tooling
- **Performance**: Optimized for speed and scalability
- **Security**: Comprehensive security measures
- **Maintainability**: Clean architecture, comprehensive documentation
- **Flexibility**: Stripe-centric but extensible design

The codebase demonstrates excellent practices for modern web development, with a focus on type safety, performance optimization, and developer experience. The Stripe-centric architecture provides a solid foundation for e-commerce while maintaining flexibility for future enhancements.
