{"$schema": "https://json.schemastore.org/tsconfig.json", "compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "allowUnreachableCode": true, "noUnusedLocals": true, "noUnusedParameters": false, "noUncheckedIndexedAccess": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@ui/*": ["./src/ui/*"], "@/components/ui/*": ["./src/ui/shadcn/*"]}}, "include": ["next-fix.d.ts", "i18n.d.ts", "**/*.mjs", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "src/script/**/*.ts"]}