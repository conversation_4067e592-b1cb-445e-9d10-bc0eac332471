# Collections/Categories Investigation Report

## Executive Summary

Your Next Store (YNS) implements a hybrid approach to collections/categories management:

- **Static categories** are defined in `src/store.config.ts` for navigation and UI structure
- **Dynamic category assignment** happens through Stripe product metadata
- Categories serve as the primary organizational structure for products

## Architecture Overview

### 1. Category Definition & Configuration

Categories are statically defined in `src/store.config.ts`:

```typescript
// src/store.config.ts:5-8
categories: [
  { name: "Apparel", slug: "apparel", image: ApparelImage },
  { name: "Accessories", slug: "accessories", image: AccessoriesImage },
];
```

This configuration is used throughout the application for:

- Navigation menu generation
- Homepage category boxes
- Footer links
- Sitemap generation

### 2. Stripe Integration

Products in Stripe use metadata fields to associate with categories:

```typescript
// Product metadata structure in Stripe:
{
  slug: "product-url-slug",      // Required for routing
  category: "apparel",           // Optional category assignment
  variant: "size-or-color",      // Optional variant info
  order: "1"                     // Optional display ordering
}
```

Category filtering happens at the commerce-kit level:

```typescript
// src/app/(store)/category/[slug]/page.tsx:40-43
products = await Commerce.productBrowse({
  first: 100,
  filter: { category: params.slug },
});
```

### 3. Data Flow

```
Stripe Product Metadata
        ↓
commerce-kit productBrowse API
        ↓
Category Page / Search Results
        ↓
ProductList Component
        ↓
Individual Product Cards
```

## Component Architecture

### Category Display Components

1. **CategoryBox** (`src/ui/category-box.tsx`)
   - Displays category with image on homepage
   - Links to category page
   - Uses static config for image source

2. **Category Page** (`src/app/(store)/category/[slug]/page.tsx`)
   - Dynamic route handler for `/category/[slug]`
   - Fetches products filtered by category
   - Handles 404 for empty/invalid categories
   - Uses ProductList for rendering

3. **ProductList** (`src/ui/products/product-list.tsx`)
   - Reusable component for displaying product grids
   - Filters out products without valid slugs
   - Handles image display and pricing

### Navigation Integration

1. **NavMenu** (`src/ui/nav/nav-menu.tsx:10-13`)

   ```typescript
   ...StoreConfig.categories.map(({ name, slug }) => ({
     label: name,
     href: `/category/${slug}`,
   }))
   ```

   - Dynamically generates navigation from config
   - Includes hardcoded "Digital" category as example

2. **Footer** (`src/ui/footer/footer.tsx:10-13`)
   - Similar pattern to NavMenu
   - Creates "Products" section with category links

### Search & Filtering

1. **Enhanced Search** (`src/lib/search/enhanced-normal-search.ts`)
   - Supports category filtering in search results
   - Extracts unique categories from all products
   - Filters products by exact category match

2. **Search Filters UI** (`src/ui/search/search-filters.tsx`)
   - Provides category dropdown in search results
   - Syncs with URL parameters
   - Allows clearing individual filters

3. **URL Parameter Sync** (`src/app/(store)/search/search-page-client.tsx`)
   - Maintains filter state in URL
   - Supports bookmarkable filtered searches
   - Debounced updates for performance

## Key Implementation Details

### 1. Category Routing

- Route: `/category/[slug]`
- Example: `/category/apparel`
- 404 handling for invalid categories

### 2. Metadata Requirements

- Products MUST have `slug` metadata for routing
- Category metadata is optional but enables filtering
- Case-insensitive category matching in search

### 3. Static vs Dynamic

- **Static**: Navigation structure, images, display names
- **Dynamic**: Product assignment, filtering, availability

### 4. Internationalization

- Category names use `deslugify()` for display
- Translation keys: `/category.metadata.title`, `/category.page.title`
- Supports localized category descriptions

## Data Model

### StoreConfig Category Type

```typescript
{
  name: string,      // Display name
  slug: string,      // URL slug
  image: ImageProps  // Next.js Image source
}
```

### Product Metadata

```typescript
{
  slug: string,      // Required
  category?: string, // Optional, matches category slug
  variant?: string,  // Optional
  order?: string     // Optional
}
```

## Limitations & Considerations

1. **No Subcategories**: Flat category structure only
2. **Static Definition**: New categories require code changes
3. **No Category Metadata**: Categories don't have descriptions, SEO data
4. **Manual Assignment**: Products must be tagged in Stripe dashboard
5. **No Multi-Category**: Products can only belong to one category

## Enhancement Opportunities

1. **Dynamic Categories**: Fetch categories from Stripe or CMS
2. **Category Pages**: Add rich content, banners, descriptions
3. **Subcategories**: Implement hierarchical category structure
4. **Multi-Category**: Allow products in multiple categories
5. **Category Attributes**: Add SEO metadata, custom ordering
6. **Admin UI**: Build category management interface

## Technical Flow Diagram

```
User clicks category →
  NavMenu/CategoryBox →
    /category/[slug] route →
      productBrowse({ filter: { category: slug }}) →
        Stripe API (filtered by metadata.category) →
          ProductList component →
            Individual product cards
```

## Summary

The collections/categories system in YNS is intentionally simple, leveraging Stripe's metadata system for product organization while maintaining a static configuration for UI consistency. This approach works well for small to medium catalogs but may need enhancement for larger, more complex product hierarchies.
