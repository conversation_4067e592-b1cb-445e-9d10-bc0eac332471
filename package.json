{"$schema": "https://json.schemastore.org/package", "name": "yournextstore", "version": "1.12.0", "license": "AGPL-3.0-only", "private": true, "type": "module", "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "biome check --write", "test": "vitest", "prepare": "bun husky", "docker:build": "docker build -t yournextstore .", "docker:run": "docker run -d -p 3000:3000 yournextstore"}, "dependencies": {"@next/mdx": "15.4.0-canary.119", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-collapsible": "1.1.11", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-icons": "1.3.2", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toggle": "1.1.9", "@radix-ui/react-toggle-group": "1.1.10", "@radix-ui/react-tooltip": "1.2.7", "@splinetool/react-spline": "4.0.0", "@splinetool/runtime": "1.10.24", "@stripe/react-stripe-js": "3.7.0", "@stripe/stripe-js": "7.4.0", "@t3-oss/env-nextjs": "0.13.8", "@types/bcrypt": "^5.0.2", "@vercel/analytics": "1.5.0", "@vercel/blob": "1.1.1", "@vercel/edge-config": "1.4.0", "@vercel/speed-insights": "1.2.0", "bcrypt": "^6.0.0", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "commerce-kit": "0.0.40", "eslint-config-next": "^15.4.0-canary.119", "focus-trap-react": "^11.0.4", "fuse.js": "^7.1.0", "intl-messageformat": "10.7.16", "jose": "6.0.11", "lucide-react": "0.525.0", "ms": "^2.1.3", "nanoid": "5.1.5", "next": "^15.4.0-canary.119", "next-mdx-remote": "5.0.0", "next-themes": "0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "resend": "^4.6.0", "schema-dts": "1.1.5", "server-only": "0.0.1", "sonner": "2.0.6", "stripe": "18.3.0", "tailwind-merge": "3.3.1", "tailwindcss-animate": "1.0.7", "trieve-ts-sdk": "0.0.122", "use-debounce": "^10.0.5", "vaul": "1.1.2", "zod": "3.25.76"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "19.8.1", "@commitlint/types": "19.8.1", "@next/env": "15.4.0-canary.119", "@semantic-release/changelog": "6.0.3", "@semantic-release/commit-analyzer": "13.0.1", "@semantic-release/git": "10.0.1", "@semantic-release/github": "11.0.3", "@semantic-release/npm": "12.0.2", "@semantic-release/release-notes-generator": "14.0.3", "@tailwindcss/forms": "0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "0.5.16", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@types/mdx": "2.0.13", "@types/node": "^24.0.12", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@vitejs/plugin-react": "4.6.0", "babel-plugin-react-compiler": "19.1.0-rc.2", "husky": "9.1.7", "lint-staged": "16.1.2", "mdx": "0.3.1", "postcss": "8.5.6", "semantic-release": "24.2.6", "sharp": "0.34.2", "tailwindcss": "^4.1.11", "tsx": "4.20.3", "typescript": "5.8.3", "vite-tsconfig-paths": "5.1.4", "vitest": "3.2.4"}, "engines": {"node": "^20.11.1 || ^22.0.0"}, "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git", "@semantic-release/github"]}, "lint-staged": {"*": ["bun biome check --write --staged --no-errors-on-unmatched --files-ignore-unknown=true"]}, "trustedDependencies": ["@tailwindcss/oxide", "@vercel/speed-insights", "esbuild", "sharp", "unrs-resolver"]}