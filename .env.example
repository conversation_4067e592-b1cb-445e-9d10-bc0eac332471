ENABLE_EXPERIMENTAL_COREPACK=1

NEXT_PUBLIC_URL=http://localhost:3000

NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
STRIPE_CURRENCY=usd
ENABLE_STRIPE_TAX=

DOCKER=

NEXT_PUBLIC_LANGUAGE=en-US

TRIEVE_API_KEY=
TRIEVE_DATASET_ID=

# OPTIONAL:
NEXT_PUBLIC_UMAMI_WEBSITE_ID=

# Authentication Configuration
# IMPORTANT: In production, use a strong password and store it as a bcrypt hash
# For development, use a plain password (will be hashed automatically)
EMAIL=<EMAIL>
PASSWORD=changeme123!

# Production bcrypt hash example (generated with: node -e "console.log(require('bcrypt').hashSync('changeme123!', 10))")
# PASSWORD=$2b$10$YourBcryptHashWouldGoHereInProductionEnvironment

# JWT Secret Key - MUST be at least 32 characters for security
# Generate with: openssl rand -base64 32
SECRET=

# Newsletter Configuration (Resend)
RESEND_API_KEY=
NEXT_PUBLIC_NEWSLETTER_ENDPOINT=https://yourdomain.com/api/newsletter
NEWSLETTER_FROM_EMAIL=<EMAIL>
NEWSLETTER_FROM_NAME=Your Store Name
