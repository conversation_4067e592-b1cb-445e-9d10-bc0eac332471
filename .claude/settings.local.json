{"permissions": {"allow": ["Bash(npm run build:*)", "Bash(bun run lint:*)", "Bash(bun run:*)", "Bash(git add:*)"]}, "hooks": {"PostToolUse": [{"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}, {"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/type-safety-guard.sh"}]}]}}