{"Global.metadata.title": "Your Next Store - Démo", "Global.metadata.description": "Un modèle Next.js pour créer instantanément votre boutique en ligne : simple, rapide et puissant.", "Global.notFound.title": "Page introuvable", "Global.notFound.description": "La page que vous souhaitez n'existe pas.", "Global.notFound.goBackLink": "Retour à l'accueil", "Global.globalError.title": "Une erreur s'est produite!", "Global.globalError.moreDetails": "Plus de détail", "Global.globalError.tryAgainButton": "<PERSON><PERSON><PERSON><PERSON>", "Global.error.title": "O<PERSON>, une erreur s'est produite!", "Global.error.goBackLink": "<PERSON><PERSON><PERSON><PERSON>", "Global.footer.newsletterTitle": "Abonnez-vous à notre infolettre", "Global.footer.categoriesTitle": "Produits", "Global.newsletter.emailPlaceholder": "Entrez votre courriel", "Global.newsletter.subscribeButton": "<PERSON>'abonner", "Global.newsletter.success": "Vous vous êtes abonné(e) avec succès à notre infolettre.", "Global.newsletter.error": "L'abonnement à notre infolettre a échoué.", "Global.nav.search.title": "Recherche", "Global.nav.search.placeholder": "Rechercher des produits…", "Global.nav.cartSummary.itemsInCart": "Articles dans le panier", "Global.nav.cartSummary.total": "Total", "Global.nav.cartSummary.totalItems": "{count, plural, =0 {Aucun article} =1 {# article} other {# articles}} dans le panier", "Global.addToCart.success": "L'article « {productName} » a été ajouté au panier", "Global.addToCart.actionButton": "A<PERSON>ter au panier", "Global.addToCart.disabled": "En rupture de stock", "Global.deliveryEstimates.atLeast": "Au moins", "Global.deliveryEstimates.upTo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Global.deliveryEstimates.businessDay": "{count, plural, =1 {# jour ouvrable} other {# jours ouvrables}}", "Global.deliveryEstimates.day": "{count, plural, =1 {# jour} other {# jours}}", "Global.deliveryEstimates.hour": "{count, plural, =1 {# heure} other {# heures}}", "Global.deliveryEstimates.month": "{count, plural, =1 {# mois} other {# mois}}", "Global.deliveryEstimates.week": "{count, plural, =1 {# semaine} other {# semaines}}", "Global.actions.shopNow": "Magasinez maintenant", "/.hero.title": "Découvrez notre collection", "/.hero.description": "Explorez nos produits soigneusement sélectionnés pour votre maison et votre style de vie.", "/.hero.action": "Magasiner maintenant", "/.hero.link": "/category/accessoires", "/search.notFound.title": "Aucun résultat trouvé pour « {query} »", "/search.notFound.description": "<PERSON><PERSON><PERSON><PERSON>, nous n'avons trouvé aucun résultat correspondant à votre recherche. Veuillez affiner votre recherche.", "/search.metadata.title": "Recherche : {query} · Your Next Store", "/search.page.title": "Recherche de « {query} »", "/search.loading.title": "Recherche en cours", "/products.metadata.title": "Tous les produits · Your Next Store", "/products.page.title": "Tous les produits", "/product.metadata.title": "{productName} · Your Next Store", "/product.page.allProducts": "Tous les produits", "/product.page.imagesTitle": "Images", "/product.page.descriptionTitle": "Description", "/product.page.variantTitle": "<PERSON><PERSON><PERSON>", "/category.metadata.title": "Catégorie {categoryName} · Your Next Store", "/category.page.title": "Catégorie : {categoryName}", "/cart.modal.title": "<PERSON><PERSON>", "/cart.modal.openFullView": "(ouvrir la vue complète)", "/cart.modal.quantity": "Quantité : {quantity}", "/cart.modal.total": "Total", "/cart.modal.shippingAndTaxesInfo": "Les frais de livraison et les taxes seront ajoutés à l'étape suivante", "/cart.modal.goToPaymentButton": "Procéder au paiement", "/cart.metadata.title": "Panier · Your Next Store", "/cart.page.title": "<PERSON><PERSON><PERSON> panier", "/cart.page.checkoutTitle": "Paiement", "/cart.page.checkoutDescription": "Veuillez entrer les informations de facturation et d'expédition ci-dessous.", "/cart.page.summaryTable.imageCol": "Image", "/cart.page.summaryTable.productCol": "Produit", "/cart.page.summaryTable.priceCol": "Prix", "/cart.page.summaryTable.quantityCol": "Quantité", "/cart.page.summaryTable.totalCol": "Total", "/cart.page.summaryTable.totalSummary": "TOTAL", "/cart.page.stripePayment.fillRequiredFields": "Veuillez remplir les champs requis.", "/cart.page.stripePayment.unexpectedError": "Une erreur inattendue s'est produite.", "/cart.page.stripePayment.billingSameAsShipping": "Adresse de facturation identique à celle de livraison", "/cart.page.stripePayment.billingAddressTitle": "Adresse de facturation", "/cart.page.stripePayment.payNowButton": "Payer maintenant", "/cart.page.stripePayment.errorTitle": "<PERSON><PERSON><PERSON>", "/cart.page.stripePayment.fullName": "Nom complet", "/cart.page.stripePayment.address1": "<PERSON><PERSON><PERSON>", "/cart.page.stripePayment.address2": "Adress<PERSON> (suite)", "/cart.page.stripePayment.postalCode": "Code postal", "/cart.page.stripePayment.city": "Ville", "/cart.page.stripePayment.state": "Province / Région", "/cart.page.stripePayment.country": "Pays", "/cart.page.stripePayment.phone": "Numéro de téléphone", "/cart.page.stripePayment.taxId": "Numéro d'identification fiscale", "/cart.page.stripePayment.taxIdPlaceholder": "ex. PL123456789", "/cart.page.formErrors.nameRequired": "Le nom est requis", "/cart.page.formErrors.cityRequired": "La ville est requise", "/cart.page.formErrors.countryRequired": "Le pays est requis", "/cart.page.formErrors.line1Required": "L'adresse est requise", "/cart.page.formErrors.postalCodeRequired": "Le code postal est requis", "/cart.empty.title": "Votre panier est vide", "/cart.empty.description": "Il semble que vous n'ayez encore rien ajouté à votre panier.", "/cart.empty.continueShoppingButton": "Continuer vos achats", "/order.metadata.title": "Confirmation de commande · Your Next Store", "/order.page.title": "Confirmation de commande", "/order.page.description": "Merci! Vous trouverez ci-dessous les détails de votre commande.", "/order.page.orderNumberTitle": "<PERSON><PERSON><PERSON><PERSON> de commande", "/order.page.productsTitle": "Produits dans cette commande", "/order.page.price": "Prix", "/order.page.quantity": "Quantité", "/order.page.total": "Total", "/order.page.detailsTitle": "<PERSON><PERSON><PERSON> de la commande", "/order.page.shippingAddress": "<PERSON><PERSON><PERSON>", "/order.page.billingAddress": "Adresse de facturation", "/order.page.taxId": "Numéro de taxe", "/order.page.paymentMethod": "Méthode de paiement", "/order.page.cardBrand": "<PERSON><PERSON> de la carte", "/order.page.last4CardDigitsLabel": "Numéro de carte se terminant par", "/order.paymentStatus.canceled": "<PERSON><PERSON><PERSON>", "/order.paymentStatus.processing": "En traitement", "/order.paymentStatus.requires_action": "Action requise", "/order.paymentStatus.requires_capture": "Capture requise", "/order.paymentStatus.requires_confirmation": "Confirmation requise", "/order.paymentStatus.requires_payment_method": "Méthode de paiement requise", "/order.paymentStatus.succeeded": "<PERSON><PERSON><PERSON><PERSON>"}