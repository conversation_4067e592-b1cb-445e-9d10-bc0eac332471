{"Global.metadata.title": "Your Next Store - 演示", "Global.metadata.description": "一个快速构建在线商店的 Next.js 模板：简单、快速、强大。", "Global.notFound.title": "未找到", "Global.notFound.description": "您查找的页面不存在。", "Global.notFound.goBackLink": "返回主页", "Global.globalError.title": "出错了！", "Global.globalError.moreDetails": "更多详情", "Global.globalError.tryAgainButton": "重试", "Global.error.title": "哎呀，出了点问题！", "Global.error.goBackLink": "重试", "Global.footer.newsletterTitle": "订阅我们", "Global.footer.categoriesTitle": "产品", "Global.newsletter.emailPlaceholder": "输入您的邮箱", "Global.newsletter.subscribeButton": "订阅", "Global.newsletter.success": "订阅成功。", "Global.newsletter.error": "订阅失败。", "Global.nav.search.title": "搜索", "Global.nav.search.placeholder": "搜索产品…", "Global.nav.cartSummary.itemsInCart": "购物车商品", "Global.nav.cartSummary.total": "总计", "Global.nav.cartSummary.totalItems": "{count, plural, =0 {购物车为空} =1 {购物车中有 # 件商品} other {购物车中有 # 件商品}}", "Global.addToCart.success": "{productName}已加入购物车", "Global.addToCart.actionButton": "添加到购物车", "Global.addToCart.disabled": "无货", "Global.deliveryEstimates.atLeast": "至少", "Global.deliveryEstimates.upTo": "最多", "Global.deliveryEstimates.businessDay": "{count, plural, =1 {# 个工作日} other {# 个工作日}}", "Global.deliveryEstimates.day": "{count, plural, =1 {# 天} other {# 天}}", "Global.deliveryEstimates.hour": "{count, plural, =1 {# 小时} other {# 小时}}", "Global.deliveryEstimates.month": "{count, plural, =1 {# 个月} other {# 个月}}", "Global.deliveryEstimates.week": "{count, plural, =1 {# 周} other {# 周}}", "Global.actions.shopNow": "立即选购", "/.hero.title": "探索精选系列", "/.hero.description": "探索我们精心挑选的家居和生活产品。", "/.hero.action": "立即选购", "/.hero.link": "/category/accessories", "/search.notFound.title": "没有与 \"{query}\" 相关的结果", "/search.notFound.description": "抱歉，我们找不到与您的搜索匹配的结果。请尝试优化您的搜索条件。", "/search.metadata.title": "搜索：{query} · Your Next Store", "/search.page.title": "搜索 \"{query}\"", "/search.loading.title": "搜索中", "/products.metadata.title": "所有产品 · Your Next Store", "/products.page.title": "所有产品", "/product.metadata.title": "{productName} · Your Next Store", "/product.page.allProducts": "所有产品", "/product.page.imagesTitle": "图片", "/product.page.descriptionTitle": "描述", "/product.page.variantTitle": "变体", "/category.metadata.title": "{categoryName} 类别 · Your Next Store", "/category.page.title": "类别：{categoryName}", "/cart.modal.title": "购物车", "/cart.modal.openFullView": "（展开完整视图）", "/cart.modal.quantity": "数量：{quantity}", "/cart.modal.total": "总计", "/cart.modal.shippingAndTaxesInfo": "运费和税金将在下一步添加", "/cart.modal.goToPaymentButton": "前往付款", "/cart.metadata.title": "购物车 · Your Next Store", "/cart.page.title": "您的购物车", "/cart.page.checkoutTitle": "结账", "/cart.page.checkoutDescription": "请在下方提供账单和送货详细信息。", "/cart.page.summaryTable.imageCol": "图片", "/cart.page.summaryTable.productCol": "产品", "/cart.page.summaryTable.priceCol": "价格", "/cart.page.summaryTable.quantityCol": "数量", "/cart.page.summaryTable.totalCol": "总计", "/cart.page.summaryTable.totalSummary": "总计", "/cart.page.stripePayment.fillRequiredFields": "请填写必填字段。", "/cart.page.stripePayment.unexpectedError": "发生了意外错误。", "/cart.page.stripePayment.billingSameAsShipping": "账单地址与送货地址相同", "/cart.page.stripePayment.billingAddressTitle": "账单地址", "/cart.page.stripePayment.payNowButton": "立即支付", "/cart.page.stripePayment.errorTitle": "错误", "/cart.page.stripePayment.fullName": "全名", "/cart.page.stripePayment.address1": "地址", "/cart.page.stripePayment.address2": "地址（第二行）", "/cart.page.stripePayment.postalCode": "邮政编码", "/cart.page.stripePayment.city": "城市", "/cart.page.stripePayment.state": "省份/地区", "/cart.page.stripePayment.country": "国家", "/cart.page.stripePayment.phone": "电话号码", "/cart.page.stripePayment.taxId": "税号", "/cart.page.stripePayment.taxIdPlaceholder": "例如：PL123456789", "/cart.page.formErrors.nameRequired": "姓名是必填项", "/cart.page.formErrors.cityRequired": "城市是必填项", "/cart.page.formErrors.countryRequired": "国家是必填项", "/cart.page.formErrors.line1Required": "地址是必填项", "/cart.page.formErrors.postalCodeRequired": "邮政编码是必填项", "/cart.empty.title": "您的购物车是空的", "/cart.empty.description": "看起来您还没有向购物车添加任何商品。", "/cart.empty.continueShoppingButton": "继续购物", "/order.metadata.title": "订单确认 · Your Next Store", "/order.page.title": "订单确认", "/order.page.description": "谢谢您！您可以在下方找到您的订单详情。", "/order.page.orderNumberTitle": "订单号", "/order.page.productsTitle": "此订单中的产品", "/order.page.price": "价格", "/order.page.quantity": "数量", "/order.page.total": "总计", "/order.page.detailsTitle": "订单详情", "/order.page.shippingAddress": "送货地址", "/order.page.billingAddress": "账单地址", "/order.page.taxId": "税号", "/order.page.paymentMethod": "支付方式", "/order.page.cardBrand": "支付卡品牌", "/order.page.last4CardDigitsLabel": "卡号末四位", "/order.paymentStatus.canceled": "已取消", "/order.paymentStatus.processing": "处理中", "/order.paymentStatus.requires_action": "需要操作", "/order.paymentStatus.requires_capture": "需要扣款", "/order.paymentStatus.requires_confirmation": "需要确认", "/order.paymentStatus.requires_payment_method": "需要支付方式", "/order.paymentStatus.succeeded": "成功"}