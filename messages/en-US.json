{"Global.metadata.title": "Your Next Store - Demo", "Global.metadata.description": "A Next.js boilerplate for building your online store instantly: simple, quick, powerful.", "Global.notFound.title": "Not found", "Global.notFound.description": "The page you were looking for does not exist.", "Global.notFound.goBackLink": "Go back home", "Global.globalError.title": "Something went wrong!", "Global.globalError.moreDetails": "More details", "Global.globalError.tryAgainButton": "Try again", "Global.error.title": "Ooops, something went wrong!", "Global.error.goBackLink": "Try again", "Global.footer.newsletterTitle": "Subscribe to our newsletter", "Global.footer.categoriesTitle": "Products", "Global.newsletter.emailPlaceholder": "Enter your email", "Global.newsletter.subscribeButton": "Subscribe", "Global.newsletter.success": "You have successfully subscribed to our newsletter.", "Global.newsletter.error": "Failed to subscribe to our newsletter.", "Global.nav.search.title": "Search", "Global.nav.search.placeholder": "Search for products…", "Global.nav.cartSummary.itemsInCart": "Items in cart", "Global.nav.cartSummary.total": "Total", "Global.nav.cartSummary.totalItems": "{count, plural, =0 {No items} =1 {# item} other {# items}} in cart", "Global.addToCart.success": "Added {productName} to cart", "Global.addToCart.actionButton": "Add to cart", "Global.addToCart.disabled": "Out of stock", "Global.deliveryEstimates.atLeast": "At least", "Global.deliveryEstimates.upTo": "Up to", "Global.deliveryEstimates.businessDay": "{count, plural, =1 {# business day} other {# business days}}", "Global.deliveryEstimates.day": "{count, plural, =1 {# day} other {# days}}", "Global.deliveryEstimates.hour": "{count, plural, =1 {# hour} other {# hours}}", "Global.deliveryEstimates.month": "{count, plural, =1 {# month} other {# months}}", "Global.deliveryEstimates.week": "{count, plural, =1 {# week} other {# weeks}}", "Global.actions.shopNow": "Shop now", "/.hero.title": "Discover our Curated Collection", "/.hero.description": "Explore our carefully selected products for your home and lifestyle.", "/.hero.action": "Shop Now", "/.hero.link": "/category/accessories", "/search.notFound.title": "No Results Found for \"{query}\"", "/search.notFound.description": "Sorry, we couldn't find any results that match your search query. Please try refining your search.", "/search.metadata.title": "Search: {query} · Your Next Store", "/search.page.title": "Searching for \"{query}\"", "/search.loading.title": "Searching for", "/products.metadata.title": "All Products · Your Next Store", "/products.page.title": "All Products", "/product.metadata.title": "{productName} · Your Next Store", "/product.page.allProducts": "All products", "/product.page.imagesTitle": "Images", "/product.page.descriptionTitle": "Description", "/product.page.variantTitle": "<PERSON><PERSON><PERSON>", "/category.metadata.title": "{categoryName} Category · Your Next Store", "/category.page.title": "Category", "/cart.modal.title": "Shopping Cart", "/cart.modal.openFullView": "(open full view)", "/cart.modal.quantity": "Quantity: {quantity}", "/cart.modal.total": "Total", "/cart.modal.shippingAndTaxesInfo": "Shipping and taxes will be added at the next step", "/cart.modal.goToPaymentButton": "Go to payment", "/cart.metadata.title": "Shopping Cart · Your Next Store", "/cart.page.title": "Your cart", "/cart.page.checkoutTitle": "Checkout", "/cart.page.checkoutDescription": "Provide billing and shipping details below.", "/cart.page.summaryTable.imageCol": "Image", "/cart.page.summaryTable.productCol": "Product", "/cart.page.summaryTable.priceCol": "Price", "/cart.page.summaryTable.quantityCol": "Quantity", "/cart.page.summaryTable.totalCol": "Total", "/cart.page.summaryTable.totalSummary": "TOTAL", "/cart.page.stripePayment.fillRequiredFields": "Please fill in the required fields.", "/cart.page.stripePayment.unexpectedError": "An unexpected error occurred.", "/cart.page.stripePayment.billingSameAsShipping": "Billing address same as shipping", "/cart.page.stripePayment.billingSameAsPayment": "Billing address same as payment", "/cart.page.stripePayment.billingAddressTitle": "Billing address", "/cart.page.stripePayment.payNowButton": "Pay now", "/cart.page.stripePayment.errorTitle": "Error", "/cart.page.stripePayment.fullName": "Full name", "/cart.page.stripePayment.address1": "Address", "/cart.page.stripePayment.address2": "Address (cont.)", "/cart.page.stripePayment.postalCode": "Postal Code", "/cart.page.stripePayment.city": "City", "/cart.page.stripePayment.state": "State / Region", "/cart.page.stripePayment.country": "Country", "/cart.page.stripePayment.phone": "Phone number", "/cart.page.stripePayment.taxId": "Tax ID", "/cart.page.stripePayment.taxIdPlaceholder": "eg. PL123456789", "/cart.page.formErrors.nameRequired": "Name is required", "/cart.page.formErrors.cityRequired": "City is required", "/cart.page.formErrors.countryRequired": "Country is required", "/cart.page.formErrors.line1Required": "Address is required", "/cart.page.formErrors.postalCodeRequired": "Postal code is required", "/cart.empty.title": "Your cart is empty", "/cart.empty.description": "Looks like you haven't added anything to your cart yet.", "/cart.empty.continueShoppingButton": "Continue shopping", "/order.metadata.title": "Order Confirmation · Your Next Store", "/order.page.title": "Order Confirmation", "/order.page.description": "Thank you! You'll find the details of your order below.", "/order.page.orderNumberTitle": "Order number", "/order.page.productsTitle": "Products in this order", "/order.page.price": "Price", "/order.page.quantity": "Quantity", "/order.page.total": "Total", "/order.page.detailsTitle": "Order details", "/order.page.shippingAddress": "Shipping address", "/order.page.billingAddress": "Billing address", "/order.page.taxId": "Tax ID", "/order.page.paymentMethod": "Payment method", "/order.page.cardBrand": "Card brand", "/order.page.last4CardDigitsLabel": "Card number ending in", "/order.paymentStatus.canceled": "Canceled", "/order.paymentStatus.processing": "Processing", "/order.paymentStatus.requires_action": "Requires Action", "/order.paymentStatus.requires_capture": "Requires Capture", "/order.paymentStatus.requires_confirmation": "Requires Confirmation", "/order.paymentStatus.requires_payment_method": "Requires Payment Method", "/order.paymentStatus.succeeded": "Succeeded"}